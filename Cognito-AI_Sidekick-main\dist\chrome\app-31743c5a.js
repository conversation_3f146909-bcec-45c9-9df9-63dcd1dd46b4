(()=>{"use strict";var e,t,s,n,r,a,o,i,l,c={1306:(e,t,s)=>{s.d(t,{A:()=>u});var n=s(96540),r=s(82951),a=s(85431);const o=1e3;let i=null,l=!1,c=null;async function d(e,t){try{const t=await async function(){return i||(l&&c||(l=!0,c=(async()=>{try{const t=await s.e(855).then(s.bind(s,91100));try{const e=chrome.runtime.getURL("pdf.worker.mjs");t.GlobalWorkerOptions.workerSrc=e||`https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${t.version}/pdf.worker.min.js`}catch(e){t.GlobalWorkerOptions.workerSrc=`https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${t.version}/pdf.worker.min.js`}return i=t,t}catch(t){throw t}finally{l=!1}})()),c)}(),n=await fetch(e);if(!n.ok)throw new Error(`Failed to fetch PDF: ${n.status} ${n.statusText}`);const r=await n.arrayBuffer(),a=await t.getDocument({data:r}).promise;let o="";for(let e=1;e<=a.numPages;e++){const t=await a.getPage(e),s=await t.getTextContent();o+=s.items.map((e=>"str"in e?e.str:"")).join(" ")+"\n\n",e%10==0||a.numPages}return o.trim()}catch(n){throw n}}const u=(e,t,s,i,l,c,u,m,h,p,x)=>{const g=(0,n.useRef)(null),f=(0,n.useRef)(null),b=(e,t,s,n,r)=>{if(g.current===e||s||n||r){if(null===g.current&&null!==e&&(""===t&&s&&!n&&!r||n&&(t.includes("Operation cancelled by user")||t.includes("Streaming operation cancelled"))))return p(!1),void x("idle");c((e=>{if(0===e.length||"assistant"!==e[e.length-1].role){if(n){const s={role:"assistant",rawContent:`Error: ${t||"Unknown operation error"}`,status:"error",timestamp:Date.now()};return[...e,s]}return e}const a=e[e.length-1],o=!0===n?"error":!0===r?"cancelled":s?"complete":"streaming";let i;if(r){const e=a.rawContent||"";i=e+(e?" ":"")+t}else i=n?`Error: ${t||"Unknown stream/handler error"}`:t;return[...e.slice(0,-1),{...a,rawContent:i,status:o,timestamp:Date.now()}]})),(s||!0===n||!0===r)&&(p(!1),x(n||r?"idle":"done"),g.current===e&&(g.current=null,f.current&&(f.current=null)))}};return{onSend:async t=>{const n=Date.now(),i=t||"";if(!l)return void p(!1);if(!i||!l)return;null!==g.current&&f.current&&f.current.abort();const v=new AbortController;f.current=v,p(!0),m(""),h("");const w=l.chatMode||"chat";x("web"===w?"searching":"page"===w?"reading":"thinking"),g.current=n;const y=i.match(/(https?:\/\/[^\s]+)/g);let j="";if(y&&y.length>0){x("searching");try{j=(await Promise.all(y.map((e=>(0,r.hj)(e,v.signal))))).map(((e,t)=>`Content from [${y[t]}]:\n${e}`)).join("\n\n")}catch(G){j="[Error scraping one or more URLs]"}x("thinking")}const N={role:"user",status:"complete",rawContent:i,timestamp:Date.now()};c((e=>[...e,N])),u("");const C={role:"assistant",rawContent:"",status:"streaming",timestamp:Date.now()+1};c((e=>[...e,C]));let k=i,S="",$="";const M=l?.models?.find((e=>e.id===l.selectedModel));if(!M)return void b(n,"Configuration error: No model selected.",!0,!0);const E=void 0;{x("thinking");const e=s.map((e=>({role:e.role,content:e.rawContent})));try{const t=await(0,r.GW)(i,l,M,E,v.signal,e);t&&t.trim()&&t!==i?(k=t,$=`**Optimized query:** "*${k}*"\n\n`):$=`**Original query:** "${k}"\n\n`}catch(B){$=`**Fallback query:** "${k}"\n\n`}}x("searching");try{if(S=await(0,r.tE)(k,l,v.signal),x("thinking"),v.signal.aborted)return}catch(H){if("AbortError"===H.name||v.signal.aborted)return;{S="";const e=`Web Search Failed: ${H instanceof Error?H.message:String(H)}`;return x("idle"),void b(n,e,!0,!0,!1)}}$&&c((e=>e.map((t=>"assistant"===t.role&&e[e.length-1]===t&&"complete"!==t.status&&"error"!==t.status&&"cancelled"!==t.status?{...t,webDisplayContent:$}:t))));const A=k,P=1e3*(l?.webLimit||1),T=P&&"string"==typeof S?S.substring(0,P):S,L=128===l?.webLimit?S:T,z=s.map((e=>({content:e.rawContent||"",role:e.role}))).concat({role:"user",content:i});let O="";if("page"===l?.chatMode){let e="";x("reading");try{const[t]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});if(t?.url&&!t.url.startsWith("chrome://")){const s=t.url,n=t.mimeType;if(s.toLowerCase().endsWith(".pdf")||n&&"application/pdf"===n)try{e=await d(s)}catch(Y){e=`Error extracting PDF content: ${Y instanceof Error?Y.message:"Unknown PDF error"}. Falling back.`}else{e=await a.A.getItem("pagestring")||""}}}catch(K){e=`Error accessing page content: ${K instanceof Error?K.message:"Unknown error"}`}const t=1e3*(l?.contextLimit||1),s="string"==typeof e?e:"",n=t&&s?s.substring(0,t):s;O=128===l?.contextLimit?s:n,h(O||""),x("thinking")}else h("");const I=l?.personas?.[l?.persona]||"",_="page"===l?.chatMode&&O?`Use the following page content for context: ${O}`:"",F=L?`Refer to this web search summary: ${L}`:"";let U="";const q=l.userName?.trim(),R=l.userProfile?.trim();q&&"user"!==q.toLowerCase()&&""!==q?(U=`You are interacting with a user named "${q}".`,R&&(U+=` Their provided profile information is: "${R}".`)):R&&(U=`You are interacting with a user. Their provided profile information is: "${R}".`);const D=[];I&&D.push(I),U&&D.push(U),_&&D.push(_),F&&D.push(F),j&&D.push(`Use the following scraped content from URLs in the user's message:\n${j}`);const W=D.join("\n\n").trim();try{if(x("thinking"),"high"===l?.computeLevel&&M)await(async(e,t,s,n,a,i,l)=>{const c=Math.max(.1,.5*(s.temperature||.7)),d=()=>{if(l.aborted)throw new Error("Operation cancelled by user")};i("Decomposing task into stages...",!1),d(),await new Promise((e=>setTimeout(e,o)));const u=`You are a planning agent. Given the original task: "${e}", break it down into the main sequential stages required to accomplish it. Output *only* a numbered list of stages. Example:\n1. First stage\n2. Second stage`,m=(await(0,r.GW)(u,s,n,a,l,[],c)).split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./)));if(i(`Monitoring: Generated Stages:\n${m.join("\n")||"[None]"}`,!1),d(),!m||0===m.length)return i("Error: Failed to decompose task into stages. Falling back to direct query.",!0),"Error: Could not decompose task.";const h=[];for(let f=0;f<m.length;f++){const t=m[f];d(),i(`Processing Stage ${f+1}/${m.length}: ${t}...`,!1);const u=`You are a planning agent. Given the stage: "${t}", break it down into the specific sequential steps needed to complete it. Output *only* a numbered list of steps. If no further breakdown is needed, output "No breakdown needed."`;d(),await new Promise((e=>setTimeout(e,o)));const p=await(0,r.GW)(u,s,n,a,l,[],c),x=p.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./)));i(`Monitoring: Generated Steps for Stage ${f+1}:\n${x.join("\n")||"[None, or direct solve]"}`,!1);let g="";if(0===x.length||p.includes("No breakdown needed")){i(`Solving Stage ${f+1} directly...`,!1),d();const c=`Complete the following stage based on the original task "${e}": "${t}"`;d(),await new Promise((e=>setTimeout(e,o))),g=await(0,r.GW)(c,s,n,a,l),i(`Monitoring: Direct Solve Result for Stage ${f+1}:\n${g}`,!1)}else{const u=[],m=2;let h="";for(let c=0;c<x.length;c+=m){const p=x.slice(c,c+m),g=c/m+1;d(),i(`Solving Step Batch ${g} for Stage ${f+1}: ${p.join(", ")}...`,!1);const b=`You are an expert problem solver. Given the stage: "${t}" and the original task: "${e}", complete the following steps.  Consider the following accumulated context from previous steps: ${h}\n\n${p.map(((e,t)=>`${c+t+1}. ${e}`)).join("\n")}\n\nProvide your answer in the same numbered format as the steps.`;d(),await new Promise((e=>setTimeout(e,o)));const v=await(0,r.GW)(b,s,n,a,l);i(`Monitoring: Raw Batch Results for Stage ${f+1}, Batch ${g}:\n${v}`,!1);const w=v.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./))).map((e=>e.replace(/^\d+\.\s*/,"")));i(`Monitoring: Parsed Batch Results for Stage ${f+1}, Batch ${g}:\n${w.join("\n")||"[None]"}`,!1);for(let e=0;e<w.length;e++){const t=w[e];u.push(t),h+=`Step ${c+e+1}: ${t}\n`}}i(`Synthesizing results for Stage ${f+1}...`,!1),d(),await new Promise((e=>setTimeout(e,o)));const p=`Synthesize the results of the following steps for stage "${t}" into a coherent paragraph:\n\n${u.map(((e,t)=>`Step ${t+1} Result:\n${e}`)).join("\n\n")}`;g=await(0,r.GW)(p,s,n,a,l,[],c),i(`Monitoring: Synthesized Result for Stage ${f+1}:\n${g}`,!1)}h.push(g),i(`Monitoring: Accumulated Stage Results so far:\n${h.map(((e,t)=>`Stage ${t+1}: ${e}`)).join("\n---\n")}`,!1)}i("Synthesizing final answer...",!1),d();const p=`Based on the results of the following stages, provide a final comprehensive answer for the original task "${e}":\n\n${h.map(((e,t)=>`Stage ${t+1} (${m[t]}):\n${e}`)).join("\n\n")}`;i(`Monitoring: Final Synthesis Prompt:\n${p}`,!1),d(),await new Promise((e=>setTimeout(e,o)));const x=await(0,r.GW)(p,s,n,a,l,[],c),g="**High Compute Breakdown:**\n\n"+h.map(((e,t)=>`**Stage ${t+1}: ${m[t]}**\n${e}`)).join("\n\n---\n\n")+`\n\n---\n**Final Synthesized Answer:**\n${x}`;return i(g,!0),g})(A,0,l,M,E,((e,t)=>b(n,e,Boolean(t))),v.signal);else if("medium"===l?.computeLevel&&M)await(async(e,t,s,n,a,i,l)=>{const c=Math.max(.1,.5*(s.temperature||.7)),d=()=>{if(l.aborted)throw new Error("Operation cancelled by user")};i("Decomposing task into subtasks...",!1),d(),await new Promise((e=>setTimeout(e,o)));const u=`You are a planning agent. Given the task: "${e}", break it down into logical subtasks needed to accomplish it. Output *only* a numbered list of subtasks.`,m=(await(0,r.GW)(u,s,n,a,l,[],c)).split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./)));if(i(`Monitoring: Generated Subtasks:\n${m.join("\n")||"[None]"}`,!1),d(),!m||0===m.length){i("Warning: Failed to decompose into subtasks. Attempting direct query.",!1),d(),await new Promise((e=>setTimeout(e,o)));const t=await(0,r.GW)(e,s,n,a,l);return i(t,!0),t}const h=[];for(let f=0;f<m.length;f+=2){const t=m.slice(f,f+2),c=f/2+1;d(),i(`Solving Subtask Batch ${c}: ${t.join(", ")}...`,!1);const u=`You are an expert problem solver. Given the task: "${e}", complete the following subtasks:\n\n${t.map(((e,t)=>`${f+t+1}. ${e}`)).join("\n")}\n\nProvide your answer in the same numbered format as the subtasks.`;d(),await new Promise((e=>setTimeout(e,o)));const p=await(0,r.GW)(u,s,n,a,l);i(`Monitoring: Raw Batch Results for Batch ${c}:\n${p}`,!1);const x=p.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./))).map((e=>e.replace(/^\d+\.\s*/,"")));i(`Monitoring: Parsed Batch Results for Batch ${c}:\n${x.join("\n")||"[None]"}`,!1);for(let e=0;e<x.length;e++)h.push(x[e])}i("Synthesizing final answer...",!1),d(),await new Promise((e=>setTimeout(e,o)));const p=`Synthesize the results of the following subtasks into a final comprehensive answer for the original task "${e}":\n\n${h.map(((e,t)=>`Subtask ${t+1} Result:\n${e}`)).join("\n\n")}`;i(`Monitoring: Final Synthesis Prompt:\n${p}`,!1);const x=await(0,r.GW)(p,s,n,a,l,[],c),g="**Medium Compute Breakdown:**\n\n"+h.map(((e,t)=>`**Subtask ${t+1}: ${m[t]}**\n${e}`)).join("\n\n---\n\n")+`\n\n---\n**Final Synthesized Answer:**\n${x}`;return i(g,!0),g})(A,0,l,M,E,((e,t)=>b(n,e,Boolean(t))),v.signal);else{const e={stream:!0},t={ollama:`${l?.ollamaUrl||""}/api/chat`},s=t[M.host||""];if(!s)return void b(n,`Configuration error: Could not determine API URL for host '${M.host}'.`,!0,!0);const a=[];""!==W.trim()&&a.push({role:"system",content:W}),a.push(...z),await(0,r.hL)(s,{...e,model:l?.selectedModel||"",messages:a,temperature:l?.temperature??.7,max_tokens:l?.maxTokens??32048,top_p:l?.topP??1,presence_penalty:l?.presencepenalty??0},((e,t,s)=>{b(n,e,Boolean(t),Boolean(s))}),E,M.host||"",v.signal)}}catch(V){if(v.signal.aborted)e&&p(!1),x("idle"),g.current===n&&(g.current=null),f.current&&f.current.signal===v.signal&&(f.current=null);else{const e=V instanceof Error?V.message:String(V);b(n,e,!0,!0)}}},onStop:()=>{const e=g.current;null!==e?(f.current&&(f.current.abort(),f.current=null),b(e,"[Operation cancelled by user]",!0,!1,!0)):(p(!1),x("idle"))}}}},5876:(e,t,s)=>{s.d(t,{$Ri:()=>n.$Ri,DSS:()=>o.DSS,IXo:()=>n.IXo,Ohp:()=>n.Ohp,RGv:()=>r.RGv,S8s:()=>n.S8s,VSk:()=>n.VSk,Wh$:()=>n.Wh$,YHj:()=>i.YHj,YrT:()=>n.YrT,cfR:()=>a.cfR,dG_:()=>r.dG_,dW_:()=>i.dW_,hkc:()=>n.hkc,irw:()=>n.irw,pcC:()=>n.pcC,pdY:()=>n.pdY,pqQ:()=>r.pqQ,yGN:()=>n.yGN,yPB:()=>r.yPB});var n=s(3),r=s(45066),a=s(71735),o=s(39197),i=s(61351);s(72832)},9828:(e,t,s)=>{s.a(e,(async(e,n)=>{try{s.d(t,{A:()=>T});var r=s(74848),a=s(96540),o=s(73790),i=s.n(o),l=s(5876),c=s(32090),d=s(83885),u=s(45284),m=s(59853),h=s(1306),p=s(48698),x=s(60523),g=s(78473),f=s(36948),b=s(53510),v=s(73968),w=s(34596),y=s(97660),j=s(85141),N=s(85431),C=e([y]);function S(){let e="",t="",s="",n="",r="",a="",o="";try{e=document.title||"";const i=5e6;document.body;if(document.body&&document.body.innerHTML.length>i){const e=document.body.cloneNode(!0);e.querySelectorAll("script, style, noscript, iframe, embed, object").forEach((e=>e.remove())),t=(e.textContent||"").replace(/\s\s+/g," ").trim(),s=document.body.innerHTML.replace(/\s\s+/g," ")}else document.body&&(t=(document.body.innerText||"").replace(/\s\s+/g," ").trim(),s=(document.body.innerHTML||"").replace(/\s\s+/g," "));n=Array.from(document.images).map((e=>e.alt)).filter((e=>e&&e.trim().length>0)).join(". "),r=Array.from(document.querySelectorAll("table")).map((e=>(e.innerText||"").replace(/\s\s+/g," "))).join("\n");const l=document.querySelector('meta[name="description"]');a=l&&l.getAttribute("content")||"";const c=document.querySelector('meta[name="keywords"]');o=c&&c.getAttribute("content")||""}catch(c){let e="Unknown extraction error";return c instanceof Error?e=c.message:"string"==typeof c&&(e=c),JSON.stringify({error:`Extraction failed: ${e}`,title:document.title||"Error extracting title",text:"",html:"",altTexts:"",tableData:"",meta:{description:"",keywords:""}})}const i=1e7;let l={title:e,text:t,html:s,altTexts:n,tableData:r,meta:{description:a,keywords:o}};if(JSON.stringify(l).length>i){const e=i-JSON.stringify({...l,text:"",html:""}).length;let t=e;l.text.length>.6*t&&(l.text=l.text.substring(0,Math.floor(.6*t))+"... (truncated)"),t=e-l.text.length,l.html.length>.8*t&&(l.html=l.html.substring(0,Math.floor(.8*t))+"... (truncated)")}return JSON.stringify(l)}async function $(){const[e]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});if(!e?.id||e.url?.startsWith("chrome://")||e.url?.startsWith("chrome-extension://")||e.url?.startsWith("about:"))return N.A.deleteItem("pagestring"),N.A.deleteItem("pagehtml"),N.A.deleteItem("alttexts"),void N.A.deleteItem("tabledata");N.A.deleteItem("pagestring"),N.A.deleteItem("pagehtml"),N.A.deleteItem("alttexts"),N.A.deleteItem("tabledata");try{const n=await chrome.scripting.executeScript({target:{tabId:e.id},func:S});if(!n||!Array.isArray(n)||0===n.length||!n[0]||"string"!=typeof n[0].result)return;const r=n[0].result;let a;try{a=JSON.parse(r)}catch(t){return}if(a.error)return;try{N.A.setItem("pagestring",a?.text??""),N.A.setItem("pagehtml",a?.html??""),N.A.setItem("alttexts",a?.altTexts??""),N.A.setItem("tabledata",a?.tableData??"")}catch(s){N.A.deleteItem("pagestring"),N.A.deleteItem("pagehtml"),N.A.deleteItem("alttexts"),N.A.deleteItem("tabledata")}}catch(n){n instanceof Error&&(n.message.includes('Cannot access contents of url "chrome://')||n.message.includes("Cannot access a chrome extension URL")||n.message.includes('Cannot access contents of url "about:'))}}y=(C.then?(await C)():C)[0];const M=()=>`chat_${Math.random().toString(16).slice(2)}`,E=({children:e,onClick:t})=>(0,r.jsx)("div",{className:(0,u.cn)("bg-[var(--active)] border border-[var(--text)] rounded-[16px] text-[var(--text)]","cursor-pointer flex items-center justify-center","text-md font-extrabold p-0.5 place-items-center relative text-center","w-16 flex-shrink-0","transition-colors duration-200 ease-in-out","hover:bg-[rgba(var(--text-rgb),0.1)]"),onClick:t,children:e}),A=[{id:"Google",icon:l.DSS,label:"Google Search"}],P=({children:e,onClick:t,isActive:s,title:n})=>(0,r.jsxs)(d.m_,{children:[(0,r.jsx)(d.k$,{children:(0,r.jsx)("div",{className:(0,u.cn)("border rounded-lg text-[var(--text)]","cursor-pointer flex items-center justify-center","p-2 place-items-center relative","w-8 h-8 flex-shrink-0","transition-colors duration-200 ease-in-out",s?"bg-[var(--active)] text-[var(--text)] border-[var(--active)] hover:brightness-95":"bg-transparent border-[var(--text)]/50 hover:bg-[rgba(var(--text-rgb),0.1)]"),onClick:t,"aria-label":n,children:e})}),(0,r.jsx)(d.ZI,{side:"top",className:"bg-[var(--active)]/80 text-[var(--text)] border-[var(--text)]/50",children:(0,r.jsx)("p",{children:n})})]}),T=()=>{const[e,t]=(0,a.useState)([]),[s,n]=(0,a.useState)(""),[o,C]=(0,a.useState)(M()),[k,S]=(0,a.useState)(""),[T,L]=(0,a.useState)(""),[z,O]=(0,a.useState)(!1),[I,_]=(0,a.useState)(!1),[F,U]=(0,a.useState)(!1),{config:q,updateConfig:R}=(0,f.UK)(),[D,W]=(0,a.useState)({id:null,url:""}),G=(0,a.useRef)(null),B=(0,a.useRef)({id:null,url:""}),[H,Y]=(0,a.useState)(!1),[K,V]=(0,a.useState)(!1),[J,Z]=(0,a.useState)("idle");(0,a.useEffect)((()=>{const e=new ResizeObserver((()=>{G.current&&(G.current.style.minHeight="100dvh",requestAnimationFrame((()=>{G.current&&(G.current.style.minHeight="")})))}));return G.current&&e.observe(G.current),()=>e.disconnect()}),[]),(0,a.useEffect)((()=>{if("page"!==q?.chatMode)return;const e=async()=>{const[e]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});if(e?.id&&e.url)return e.url.startsWith("chrome://")||e.url.startsWith("chrome-extension://")||e.url.startsWith("about:")?(B.current.id===e.id&&B.current.url===e.url||(N.A.deleteItem("pagestring"),N.A.deleteItem("pagehtml"),N.A.deleteItem("alttexts"),N.A.deleteItem("tabledata")),B.current={id:e.id,url:e.url},void W({id:e.id,url:e.url})):void(e.id===B.current.id&&e.url===B.current.url||(B.current={id:e.id,url:e.url},W({id:e.id,url:e.url}),await $()))};e();const t=t=>{chrome.tabs.get(t.tabId,(t=>{chrome.runtime.lastError||e()}))},s=(t,s,n)=>{n.active&&("complete"===s.status||s.url&&"complete"===n.status)&&e()};return chrome.tabs.onActivated.addListener(t),chrome.tabs.onUpdated.addListener(s),()=>{chrome.tabs.onActivated.removeListener(t),chrome.tabs.onUpdated.removeListener(s),B.current={id:null,url:""}}}),[q?.chatMode]),(0,a.useEffect)((()=>{const e=e=>{};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}),[q?.chatMode,R,I,F]);const{chatTitle:Q,setChatTitle:X}=(0,m.S)(z,e,s),{onSend:ee,onStop:te}=(0,h.A)(z,s,e,k,q,t,n,S,L,O,Z);(0,p.N)();const se=()=>{t([]),L(""),S(""),O(!1),R({chatMode:"web",computeLevel:"low"}),Z("idle"),n(""),X(""),C(M()),U(!1),_(!1),G.current&&(G.current.scrollTop=0)},ne=async()=>{try{const t=(await i().keys()).filter((e=>e.startsWith("chat_")));if(0===t.length&&0===e.length)return;await Promise.all(t.map((e=>i().removeItem(e)))),se()}catch(t){}};(0,a.useEffect)((()=>{if(e.length>0&&!F&&!I){const t={id:o,title:Q||`Chat ${new Date(Date.now()).toLocaleString()}`,turns:e,last_updated:Date.now(),model:q?.selectedModel,chatMode:q?.chatMode,webMode:"web"===q?.chatMode?q.webMode:void 0};i().setItem(o,t).catch((e=>{}))}}),[o,e,Q,q?.selectedModel,q?.chatMode,q?.webMode,F,I]),(0,a.useEffect)((()=>{if("done"===J||"idle"===J){const e=setTimeout((()=>{Z("idle")}),1500);return()=>clearTimeout(e)}}),[J]),(0,a.useEffect)((()=>{let e=!1;return(async()=>{if(!e){se();try{const[t]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});!e&&t?.id&&t.url?(W({id:t.id,url:t.url}),(t.url.startsWith("chrome://")||t.url.startsWith("chrome-extension://")||t.url.startsWith("about:"))&&(N.A.deleteItem("pagestring"),N.A.deleteItem("pagehtml"),N.A.deleteItem("alttexts"),N.A.deleteItem("tabledata"),B.current={id:null,url:""})):e||(B.current={id:null,url:""},W({id:null,url:""}),N.A.deleteItem("pagestring"),N.A.deleteItem("pagehtml"),N.A.deleteItem("alttexts"),N.A.deleteItem("tabledata"))}catch(t){e||(B.current={id:null,url:""},W({id:null,url:""}),N.A.deleteItem("pagestring"),N.A.deleteItem("pagehtml"),N.A.deleteItem("alttexts"),N.A.deleteItem("tabledata"))}}})(),()=>{e=!0,N.A.deleteItem("pagestring"),N.A.deleteItem("pagehtml"),N.A.deleteItem("alttexts"),N.A.deleteItem("tabledata"),se(),B.current={id:null,url:""}}}),[]);return(0,r.jsx)(d.Bc,{delayDuration:300,children:(0,r.jsxs)("div",{ref:G,className:(0,u.cn)("w-full h-dvh p-0 overflow-hidden","flex flex-col bg-[var(--bg)]"),children:[(0,r.jsx)(b.Y,{chatTitle:Q,deleteAll:ne,downloadImage:()=>(0,y.GV)(e),downloadJson:()=>(0,y.xD)(e),downloadText:()=>(0,y.mR)(e),downloadMarkdown:()=>(0,y.ii)(e),historyMode:F,reset:se,setHistoryMode:U,setSettingsMode:_,settingsMode:I}),(0,r.jsxs)("div",{className:"flex flex-col flex-1 min-h-0 no-scrollbar overflow-y-auto relative",children:[I&&(0,r.jsx)(j.w,{}),!I&&F&&(0,r.jsx)(g.D,{className:"flex-1 w-full min-h-0",loadChat:e=>{X(e.title||""),t(e.turns),C(e.id),U(!1),Z("idle"),_(!1),"page"!==e.chatMode&&(N.A.deleteItem("pagestring"),N.A.deleteItem("pagehtml"),N.A.deleteItem("alttexts"),N.A.deleteItem("tabledata"),B.current={id:null,url:""})},onDeleteAll:ne}),!I&&!F&&(0,r.jsxs)("div",{className:"flex flex-col flex-1 min-h-0 relative",children:[(0,r.jsx)(w.B,{isLoading:z,turns:e,settingsMode:I,onReload:()=>{t((e=>{if(e.length<2)return e;const t=e[e.length-1],s=e[e.length-2];return"assistant"===t.role&&"user"===s.role?(n(s.rawContent),e.slice(0,-2)):e})),O(!1),Z("idle")},onEditTurn:(e,s)=>{t((t=>{const n=[...t];return n[e]&&(n[e]={...n[e],rawContent:s}),n}))}}),0===e.length&&!q?.chatMode&&(0,r.jsxs)("div",{className:"fixed bottom-20 left-4 flex flex-col gap-2 z-[5] bg-card/80 backdrop-blur-md p-2 rounded-xl border border-border/50 shadow-lg",children:[(0,r.jsxs)(d.m_,{children:[(0,r.jsx)(d.k$,{asChild:!0,children:(0,r.jsx)(c.$,{"aria-label":"Cycle compute level",variant:"ghost",size:"icon",onClick:()=>{const e=q.computeLevel;R({computeLevel:"low"===e?"medium":"medium"===e?"high":"low"})},className:(0,u.cn)("hover:bg-secondary/70","high"===q.computeLevel?"text-red-600":"medium"===q.computeLevel?"text-orange-300":"text-[var(--text)]"),children:(0,r.jsx)(l.cfR,{})})}),(0,r.jsx)(d.ZI,{side:"right",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)] max-w-80",children:(0,r.jsx)("p",{children:`Compute Level: ${q.computeLevel?.toUpperCase()}. Click to change. [Warning]: beta feature and resource costly.`})})]}),(0,r.jsxs)(d.m_,{children:[(0,r.jsx)(d.k$,{asChild:!0,children:(0,r.jsx)(c.$,{"aria-label":"Add Web Search Results to LLM Context",variant:"ghost",size:"icon",onClick:()=>{R({chatMode:"web",webMode:q.webMode||A[0].id})},className:"text-[var(--text)] hover:bg-secondary/70",children:(0,r.jsx)(l.pqQ,{})})}),(0,r.jsx)(d.ZI,{side:"right",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:(0,r.jsx)("p",{children:"Add Web Search Results to LLM Context"})})]}),(0,r.jsxs)(d.m_,{children:[(0,r.jsx)(d.k$,{asChild:!0,children:(0,r.jsx)(c.$,{"aria-label":"Add Current Web Page to LLM Context",variant:"ghost",size:"icon",onClick:()=>{R({chatMode:"page"})},className:"text-[var(--text)] hover:bg-secondary/70",children:(0,r.jsx)(l.RGv,{})})}),(0,r.jsx)(d.ZI,{side:"right",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:(0,r.jsx)("p",{children:"Add Current Web Page to LLM Context"})})]})]}),"page"===q?.chatMode&&(0,r.jsx)("div",{className:(0,u.cn)("fixed bottom-16 left-1/2 -translate-x-1/2","flex flex-row justify-center","w-fit h-8 z-[2]","transition-all duration-200 ease-in-out","opacity-100 translate-y-0","bg-card/80 backdrop-blur-md px-3 py-2 rounded-xl border border-border/50 shadow-lg"),onMouseEnter:()=>Y(!0),onMouseLeave:()=>Y(!1),children:(0,r.jsxs)("div",{className:"flex items-center space-x-6 max-w-full overflow-x-auto px-0",children:[(0,r.jsxs)(d.m_,{children:[(0,r.jsx)(d.k$,{children:(0,r.jsx)(E,{onClick:()=>ee("Provide your summary."),children:"TLDR"})}),(0,r.jsx)(d.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,r.jsx)("p",{children:"Quick Summary"})})]}),(0,r.jsxs)(d.m_,{children:[(0,r.jsx)(d.k$,{children:(0,r.jsx)(E,{onClick:()=>ee("Extract all key figures, names, locations, and dates mentioned on this page and list them."),children:"Facts"})}),(0,r.jsx)(d.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,r.jsx)("p",{children:"Numbers, events, names"})})]}),(0,r.jsxs)(d.m_,{children:[(0,r.jsx)(d.k$,{children:(0,r.jsx)(E,{onClick:()=>ee("Find positive developments, achievements, or opportunities mentioned on this page."),children:"Yay!"})}),(0,r.jsx)(d.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,r.jsx)("p",{children:"Good news"})})]}),(0,r.jsxs)(d.m_,{children:[(0,r.jsx)(d.k$,{children:(0,r.jsx)(E,{onClick:()=>ee("Find concerning issues, risks, or criticisms mentioned on this page."),children:"Oops"})}),(0,r.jsx)(d.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,r.jsx)("p",{children:"Bad news"})})]})]})}),"web"===q?.chatMode&&(0,r.jsx)("div",{className:(0,u.cn)("fixed bottom-14 left-1/2 -translate-x-1/2","flex flex-row justify-center","w-fit h-10 z-[2]","transition-all duration-200 ease-in-out","opacity-100 translate-y-0","bg-card/80 backdrop-blur-md px-3 py-2 rounded-xl border border-border/50 shadow-lg"),onMouseEnter:()=>V(!0),onMouseLeave:()=>V(!1),children:(0,r.jsx)("div",{className:"flex items-center space-x-4 max-w-full overflow-x-auto px-4 py-1",children:A.map((e=>(0,r.jsx)(P,{onClick:()=>{R({webMode:e.id,chatMode:"web"})},isActive:q.webMode===e.id,title:e.label,children:(0,r.jsx)(e.icon,{size:18})},e.id)))})})]})]}),!I&&!F&&(0,r.jsx)("div",{className:"p-2 relative z-[10]",children:(0,r.jsx)(v.p,{isLoading:z,message:s,setMessage:n,onSend:()=>ee(s),onStopRequest:te})}),q?.backgroundImage?(0,r.jsx)(x.V,{}):null]})})};n()}catch(k){n(k)}}))},34596:(e,t,s)=>{s.d(t,{B:()=>v});var n=s(74848),r=s(96540),a=s(3),o=(s(62570),s(62199)),i=s(5876),l=s(89696),c=s(32090),d=s(45284),u=s(90288),m=s(53037),h=s(66241),p=s(36948),x=s(26508);const g=({content:e})=>{const[t,s]=(0,r.useState)(!1);return(0,n.jsx)("div",{className:"mb-2",children:(0,n.jsxs)(u.Nt,{open:t,onOpenChange:s,className:"w-full",children:[(0,n.jsx)(u.R6,{asChild:!0,children:(0,n.jsx)(c.$,{variant:"outline",size:"sm",className:(0,d.cn)("mb-1","border-foreground text-foreground hover:text-accent-foreground"),children:t?"Hide Thoughts":"Show Thoughts"})}),(0,n.jsx)(u.Ke,{children:(0,n.jsx)("div",{className:(0,d.cn)("p-3 rounded-md border border-dashed","bg-muted","border-muted-foreground","text-muted-foreground"),children:(0,n.jsx)("div",{className:"markdown-body",children:(0,n.jsx)(o.Ay,{remarkPlugins:[[m.A,{singleTilde:!1}],h.A],components:f,children:e})})})})]})})},f={...x.Af,pre:e=>(0,n.jsx)(x.AC,{...e,buttonVariant:"copy-button"})},b=({turn:e,index:t,isEditing:s,editText:a,onStartEdit:u,onSetEditText:x,onSaveEdit:b,onCancelEdit:v})=>{const{config:w}=(0,p.UK)(),y=(e.rawContent||"").split(/(<think>[\s\S]*?<\/think>)/g).filter((e=>e&&""!==e.trim())),j=/<think>([\s\S]*?)<\/think>/;return(0,r.useEffect)((()=>{if(!s)return;const e=e=>{"Escape"===e.key?(e.preventDefault(),e.stopPropagation(),v()):"Enter"!==e.key||e.shiftKey||e.altKey||e.metaKey||a.trim()&&(e.preventDefault(),e.stopPropagation(),b())};return document.addEventListener("keydown",e,!0),()=>{document.removeEventListener("keydown",e,!0)}}),[s,v,b,a]),(0,n.jsx)("div",{className:(0,d.cn)("text-base text-left relative","w-full px-4 py-3","assistant"===e.role?"text-foreground":"text-foreground/90","chatMessage",s?"editing":"",w&&"number"==typeof w.fontSize&&w.fontSize<=15?"font-semibold":""),onDoubleClick:()=>{s||u(t,e.rawContent)},children:s?(0,n.jsxs)("div",{className:"flex flex-col space-y-2 items-stretch w-full p-1",children:[(0,n.jsx)(l.T,{autosize:!0,value:a,onChange:e=>x(e.target.value),placeholder:"Edit your message...",className:(0,d.cn)("w-full rounded-md border bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground","border-input","text-foreground","hover:border-primary focus-visible:border-primary focus-visible:ring-0","min-h-[60px]"),minRows:3,autoFocus:!0}),(0,n.jsxs)("div",{className:"flex font-mono justify-end space-x-2",children:[(0,n.jsxs)(c.$,{size:"sm",variant:"outline",onClick:b,title:"Save changes",children:[(0,n.jsx)(i.YrT,{className:"h-4 w-4 mr-1"})," Save"]}),(0,n.jsxs)(c.$,{variant:"outline",size:"sm",onClick:v,title:"Discard changes",children:[(0,n.jsx)(i.yGN,{className:"h-4 w-4 mr-1"})," Exit"]})]})]}):(0,n.jsxs)("div",{className:"message-markdown markdown-body relative z-[1] text-foreground",children:[(0,n.jsx)("div",{className:(0,d.cn)("text-apple-caption1 font-medium mb-2","assistant"===e.role?"text-muted-foreground":"text-muted-foreground/80"),children:"assistant"===e.role?"Assistant":"You"}),"assistant"===e.role&&e.webDisplayContent&&(0,n.jsx)("div",{className:"message-prefix",children:(0,n.jsx)(o.Ay,{remarkPlugins:[[m.A,{singleTilde:!1}],h.A],components:f})}),y.map(((e,t)=>{const s=e.match(j);return s&&s[1]?(0,n.jsx)(g,{content:s[1]},`think_${t}`):(0,n.jsx)("div",{className:"message-content",children:(0,n.jsx)(o.Ay,{remarkPlugins:[[m.A,{singleTilde:!1}],h.A],components:f,children:e})},`content_${t}`)}))]})})},v=({turns:e=[],isLoading:t=!1,onReload:s=()=>{},settingsMode:o=!1,onEditTurn:i})=>{const[l,u]=(0,r.useState)(-1),[m,h]=(0,r.useState)(null),[x,g]=(0,r.useState)(""),{config:f}=(0,p.UK)(),v=(0,r.useRef)(null),w=(0,r.useRef)(null);(0,r.useLayoutEffect)((()=>{const e=w.current;if(e){e.scrollHeight-e.scrollTop-e.clientHeight<200&&(e.scrollTop=e.scrollHeight)}}),[e]);const y=e=>{navigator.clipboard.writeText(e)},j=(e,t)=>{h(e),g(t)},N=()=>{h(null),g("")},C=()=>{null!==m&&x.trim()&&i(m,x),N()};return(0,n.jsxs)("div",{ref:w,id:"messages",className:(0,d.cn)("flex flex-col flex-grow w-full overflow-y-auto pb-2 pt-2","no-scrollbar"),style:{background:"var(--bg)",opacity:o?0:1},children:[e.map(((t,r)=>t&&(0,n.jsxs)("div",{className:(0,d.cn)("flex items-start w-full relative group","border-b border-border/30 last:border-b-0",(t.role,"justify-start")),onMouseEnter:()=>u(r),onMouseLeave:()=>u(-1),children:["assistant"===t.role&&(0,n.jsxs)("div",{className:(0,d.cn)("flex flex-row items-center space-x-1 ml-2 transition-opacity duration-100",l===r?"opacity-100 pointer-events-auto":"opacity-0 pointer-events-none"),children:[m!==r&&(0,n.jsx)(c.$,{"aria-label":"Copy",variant:"message-action",size:"xs",onClick:()=>y(t.rawContent),title:"Copy message",children:(0,n.jsx)(a.nxz,{className:"text-[var(--text)]"})}),r===e.length-1&&(0,n.jsx)(c.$,{"aria-label":"Reload",variant:"message-action",size:"xs",onClick:s,title:"Reload last prompt",children:(0,n.jsx)(a.jEl,{className:"text-[var(--text)]"})})]}),(0,n.jsx)(b,{turn:t,index:r,isEditing:m===r,editText:x,onStartEdit:j,onSetEditText:g,onSaveEdit:C,onCancelEdit:N}),"user"===t.role&&(0,n.jsx)("div",{className:(0,d.cn)("flex flex-row items-center space-x-1 ml-2 transition-opacity duration-100",l===r?"opacity-100 pointer-events-auto":"opacity-0 pointer-events-none"),children:m!==r&&(0,n.jsx)(c.$,{"aria-label":"Copy",variant:"message-action",size:"xs",onClick:()=>y(t.rawContent),title:"Copy message",children:(0,n.jsx)(a.nxz,{className:"text-[var(--text)]"})})})]},t.timestamp||`turn_${r}`))),(0,n.jsx)("div",{ref:v,style:{height:"1px"}})]})}},36948:(e,t,s)=>{s.d(t,{UK:()=>c,sG:()=>l});var n=s(74848),r=s(96540),a=s(85431);const o=(0,r.createContext)({}),i={personas:{Scholar:"You are The Scholar, an analytical academic researcher specializing in web search analysis. When analyzing search results, provide thorough academic-style analysis with structured insights. Behavior: Organize findings into clear sections with proper citations. Analyze credibility of sources and highlight methodological strengths/weaknesses. Present comprehensive summaries with evidence-based conclusions. Include relevant statistics and data points. Mannerisms: Use formal academic tone. Structure responses with clear headings. Always cite sources and assess their reliability.",Executive:"You are The Executive, a strategic business leader focused on actionable intelligence. When analyzing search results, distill information into concise strategic insights. Behavior: Identify key business implications and market opportunities. Provide executive summaries with clear recommendations. Focus on competitive advantages and strategic positioning. Highlight actionable next steps. Mannerisms: Be direct and results-oriented. Use bullet points for clarity. Think in terms of ROI and strategic value.",Storyteller:"You are The Storyteller, a master of engaging narrative who makes information accessible. When analyzing search results, weave findings into compelling stories. Behavior: Create narrative flow that connects different pieces of information. Use analogies and examples to illustrate complex concepts. Make dry data engaging through storytelling techniques. Connect information to human experiences. Mannerisms: Use vivid language and metaphors. Create logical narrative progression. Make complex topics relatable.",Skeptic:'You are The Skeptic, a critical analyst who questions everything. When analyzing search results, highlight biases, contradictions, and missing information. Behavior: Identify potential conflicts of interest in sources. Point out logical fallacies and weak evidence. Highlight what information is missing or unclear. Question assumptions and challenge conventional wisdom. Mannerisms: Use phrases like "However," "It should be noted," and "The evidence suggests." Always present counterarguments.',Mentor:"You are The Mentor, an educational guide focused on learning and growth. When analyzing search results, explain concepts clearly with supportive guidance. Behavior: Break down complex topics into digestible lessons. Provide context and background information. Offer learning resources and next steps. Encourage deeper exploration of topics. Mannerisms: Use encouraging language. Provide step-by-step explanations. Include educational tips and learning opportunities.",Investigator:"You are The Investigator, a methodical fact-checker focused on source credibility. When analyzing search results, systematically verify information and assess reliability. Behavior: Cross-reference information across multiple sources. Evaluate source credibility and potential biases. Identify primary vs. secondary sources. Flag unverified claims and missing evidence. Mannerisms: Use systematic approach to verification. Clearly distinguish between verified facts and claims. Provide source reliability assessments.",Pragmatist:'You are The Pragmatist, a solution-focused analyst emphasizing practical applications. When analyzing search results, focus on actionable insights and real-world implementation. Behavior: Identify practical solutions and implementation strategies. Focus on cost-effective and feasible approaches. Provide step-by-step action plans. Consider resource requirements and constraints. Mannerisms: Use practical language. Focus on "how-to" guidance. Emphasize feasibility and implementation.',Enthusiast:'You are The Enthusiast, an energetic discoverer who presents findings with excitement. When analyzing search results, highlight fascinating discoveries and breakthrough insights. Behavior: Emphasize exciting developments and innovations. Connect findings to broader trends and possibilities. Celebrate interesting discoveries and connections. Inspire curiosity about the topic. Mannerisms: Use enthusiastic language and exclamation points. Highlight "amazing" and "fascinating" aspects. Express genuine excitement about discoveries.',Curator:"You are The Curator, a sophisticated synthesizer of premium insights. When analyzing search results, provide refined, high-quality analysis with elegant presentation. Behavior: Select only the most valuable and relevant information. Present insights with sophisticated analysis and nuanced understanding. Focus on quality over quantity. Provide polished, professional summaries. Mannerisms: Use refined language and elegant phrasing. Focus on premium insights. Present information with sophisticated analysis.",Friend:'You are The Friend, a casual conversationalist sharing interesting discoveries. When analyzing search results, present findings in a friendly, approachable manner. Behavior: Share information like you would with a close friend. Use conversational tone and relatable examples. Make complex topics feel accessible and interesting. Include personal observations and casual insights. Mannerisms: Use casual, friendly language. Include phrases like "You know what\'s interesting?" and "I found this cool thing." Make information feel like a friendly conversation.'},generateTitle:!0,backgroundImage:!1,persona:"Scholar",webMode:"Google",webLimit:60,serpMaxLinksToVisit:3,contextLimit:60,maxTokens:32480,temperature:.7,topP:.95,presencepenalty:0,models:[],selectedModel:void 0,chatMode:"web",ollamaUrl:"http://localhost:11434",ollamaConnected:!1,fontSize:14,panelOpen:!1,computeLevel:"low",userName:"user",userProfile:"",theme:"light"},l=({children:e})=>{const[t,s]=(0,r.useState)(i),[l,c]=(0,r.useState)(!0);(0,r.useEffect)((()=>{(async()=>{c(!1)})()}),[]),(0,r.useEffect)((()=>{const e=t?.fontSize||i.fontSize;document.documentElement.style.setProperty("font-size",`${e}px`)}),[l,t?.fontSize]),(0,r.useEffect)((()=>{"dark"===(t?.theme||i.theme)?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")}),[l,t?.theme]);return l?(0,n.jsx)("div",{children:"Loading..."}):(0,n.jsx)(o,{value:{config:t,updateConfig:e=>{s((t=>{const s={...t,...e};return a.A.setItem("config",JSON.stringify(s)).catch((e=>{})),s}))}},children:e})},c=()=>(0,r.use)(o)},43190:(e,t,s)=>{s.d(t,{g:()=>d});var n=s(75923),r=s(79448),a=s(27346),o=s(73207),i=s(45886);const l={...s(86108).z2,...i.z2},c=((0,o.nK)(l),a.P,(0,n.N0)(),r.logger,[(0,o.nK)(l),a.P,(0,n.N0)(),r.logger]);(0,o.nK)(l),r.logger;const d=e=>{const t=new o.il({channelName:e});return(0,o.Tw)(t,...c),t}},45886:(e,t,s)=>{s.d(t,{z2:()=>l});var n=s(75923);const r={isLoaded:!1},a=(0,n.Z0)({name:"content",initialState:r,reducers:{reset:()=>r,contentLoaded:e=>{e.isLoaded=!0}}}),{actions:o,reducer:i}=a,l={}},48698:(e,t,s)=>{s.d(t,{N:()=>a});var n=s(96540),r=s(36948);const a=()=>{const{config:e,updateConfig:t}=(0,r.UK)(),s=(0,n.useRef)(0),a=[{host:"ollama",isEnabled:e=>!!e.ollamaUrl&&!0===e.ollamaConnected,getUrl:e=>`${e.ollamaUrl}/api/tags`,parseFn:(e,t)=>(e?.models??[]).map((e=>({...e,id:e.id??e.name,host:t}))),onFetchFail:(e,t)=>t({ollamaConnected:!1,ollamaUrl:""})}];return{fetchAllModels:(0,n.useCallback)((async()=>{const n=Date.now();if(n-s.current<3e4)return;s.current=n;const r=e;if(!r)return;const o=await Promise.allSettled(a.map((async e=>{if(!e.isEnabled(r))return{host:e.host,models:[],status:"disabled"};const s=e.getUrl(r);if(!s)return{host:e.host,models:[],status:"error",error:"Invalid URL"};const n=e.getFetchOptions?e.getFetchOptions(r):{},a=await(async(e,t={})=>{try{const s=await fetch(e,t);if(!s.ok)return;return await s.json()}catch(s){return}})(s,n);if(a){const t=e.parseFn(a,e.host);return{host:e.host,models:t,status:"success"}}return e.onFetchFail&&e.onFetchFail(r,t),{host:e.host,models:[],status:"error",error:"Fetch failed"}})));let i=[];o.forEach((e=>{"fulfilled"===e.status&&"success"===e.value.status&&i.push(...e.value.models)}));const l=r.models||[],c={};((e,t)=>{if(e.length!==t.length)return!0;const s=(e,t)=>e.id.localeCompare(t.id),n=[...e].sort(s),r=[...t].sort(s);return JSON.stringify(n)!==JSON.stringify(r)})(i,l)&&(c.models=i);const d=r.selectedModel,u=c.models||l,m=d&&u.some((e=>e.id===d))?d:u[0]?.id;(m!==d||c.models)&&(c.selectedModel=m),Object.keys(c).length>0&&t(c)}),[e,t,3e4,a])}}},53003:(e,t,s)=>{s.a(e,(async(e,t)=>{try{var n=s(74848),r=s(5338),a=s(71468),o=s(43190),i=s(86174),l=s(9828),c=s(36948),d=(s(62570),e([l]));l=(d.then?(await d)():d)[0];const u=(0,o.g)(i.A.ContentPort),m=document.getElementById("root");u.ready().then((()=>{if(null==m)throw new Error("Root container not found");(0,r.createRoot)(m).render((0,n.jsx)(a.Kq,{store:u,children:(0,n.jsx)(c.sG,{children:(0,n.jsx)(l.A,{})})}))})),t()}catch(u){t(u)}}))},53510:(e,t,s)=>{s.d(t,{Y:()=>S});var n=s(74848),r=s(96540),a=s(5876),o=s(36948),i=s(45284),l=s(32090),c=s(42777),d=s(29018),u=s(26532),m=s(83885),h=s(48698),p=s(67520);const x=({isOpen:e,onOpenChange:t,config:s,updateConfig:o,setSettingsMode:x,setHistoryMode:g})=>{const[f,b]=r.useState(""),[v,w]=r.useState(!1),{fetchAllModels:y}=(0,h.N)(),j=r.useRef(null),N=(0,r.useRef)(null),[C,k]=r.useState({top:0,left:0,width:0}),S=s?.persona||"default",$=p.rm[S]||p.rm.default,M=s?.models?.filter((e=>e.id.toLowerCase().includes(f.toLowerCase())||e.host?.toLowerCase()?.includes(f.toLowerCase())))||[];return(0,r.useEffect)((()=>{e&&(b(""),w(!1))}),[e]),(0,r.useEffect)((()=>{if(v&&N.current){const e=N.current.getBoundingClientRect();k({top:e.bottom+window.scrollY,left:e.left+window.scrollX,width:e.width})}}),[v]),(0,r.useEffect)((()=>{if(!v)return;const e=()=>{if(N.current){const e=N.current.getBoundingClientRect();k({top:e.bottom+window.scrollY,left:e.left+window.scrollX,width:e.width})}};return window.addEventListener("resize",e),window.addEventListener("scroll",e,!0),()=>{window.removeEventListener("resize",e),window.removeEventListener("scroll",e,!0)}}),[v]),(0,n.jsxs)(c.cj,{open:e,onOpenChange:t,children:[(0,n.jsx)(c.YP,{}),(0,n.jsxs)(c.h,{variant:"themedPanel",side:"left",className:(0,i.cn)("p-0 border-r-0","w-[22.857rem] sm:w-[27.143rem]","flex flex-col h-full max-h-screen","[&>button]:hidden","settings-drawer-content","overflow-y-auto"),style:{height:"100dvh"},ref:j,onOpenAutoFocus:e=>{e.preventDefault(),j.current?.focus({preventScroll:!0})},children:[(0,n.jsx)("div",{className:(0,i.cn)("border border-[var(--active)]","sticky top-0 z-10 p-0")}),(0,n.jsxs)(c.Fm,{className:"px-4 pt-4 pb-4",children:[(0,n.jsx)("div",{className:"flex items-center justify-between mb-2 relative z-10",children:(0,n.jsxs)(m.m_,{children:[(0,n.jsx)(m.k$,{asChild:!0,children:(0,n.jsx)(l.$,{variant:"ghost",size:"sm","aria-label":"Close Settings",className:"text-[var(--text)] rounded-md relative top-[1px]",onClick:()=>t(!1),children:(0,n.jsx)(a.yGN,{size:"20px"})})}),(0,n.jsx)(m.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:" Close Settings "})]})}),(0,n.jsx)(c.qp,{className:"text-center font-['Bruno_Ace_SC'] tracking-tight -mt-10 cognito-title-container",children:(0,n.jsxs)("a",{href:"https://github.com/3-ark/Cognito",target:"_blank",rel:"noopener noreferrer",className:(0,i.cn)("text-xl font-semibold text-[var(--text)] bg-[var(--active)] inline-block px-3 py-1 rounded-md no-underline","chromepanion-title-blade-glow"),children:["CHROMEPANION ",(0,n.jsxs)("sub",{className:"contrast-200 text-[0.5em]",children:["v","3.7.4"]})]})}),(0,n.jsx)(c.Qs,{className:"text-center font-['Bruno_Ace_SC'] text-[var(--text)] leading-tight mt-2",children:"Settings"})]}),(0,n.jsx)("div",{className:(0,i.cn)("flex flex-col h-full overflow-y-auto settings-drawer-body","no-scrollbar"),children:(0,n.jsxs)("div",{className:(0,i.cn)("flex flex-col space-y-5 flex-1","px-6","py-4"),children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"flex items-center justify-between mt-5 mb-3",children:(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("label",{htmlFor:"persona-select",className:"text-[var(--text)] opacity-80 font-['Bruno_Ace_SC'] text-lg shrink-0",children:"Persona"}),(0,n.jsx)("span",{className:"text-lg",children:$})]})}),(0,n.jsx)("div",{className:"w-full",children:(0,n.jsxs)(d.l6,{value:S,onValueChange:e=>o({persona:e}),children:[(0,n.jsx)(d.bq,{id:"persona-select",variant:"settingsPanel",className:"w-full font-['Space_Mono',_monospace] data-[placeholder]:text-muted-foreground",children:(0,n.jsx)(d.yv,{placeholder:"Select Persona..."})}),(0,n.jsx)(d.gC,{variant:"settingsPanel",children:Object.keys(s?.personas||{}).map((e=>(0,n.jsx)(d.eb,{value:e,className:(0,i.cn)("hover:brightness-95 focus:bg-[var(--active)]","font-['Space_Mono',_monospace]"),children:e},e)))})]})})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"model-input",className:"block text-[var(--text)] opacity-80 text-lg font-['Bruno_Ace_SC']",children:"Model"}),(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)(u.p,{id:"model-input",ref:N,value:v?f:s?.selectedModel||"",placeholder:v?"Search models...":s?.selectedModel||"Select model...",onChange:e=>b(e.target.value),onFocus:()=>{b(""),w(!0),y()},className:(0,i.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9 font-['Space_Mono',_monospace]","focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-95","mb-2 mt-3","ring-1 ring-inset ring-[var(--active)]/50")}),v&&(0,n.jsx)("div",{className:"fixed inset-0 z-50",onClick:()=>w(!1),children:(0,n.jsx)("div",{className:(0,i.cn)("absolute left-0 right-0","bg-[var(--bg)]","border border-[var(--active)]/20","rounded-xl shadow-lg","no-scrollbar","overflow-y-auto"),style:{maxHeight:"min(calc(50vh - 6rem), 300px)",top:`${C.top}px`,left:`${C.left}px`,width:`${C.width}px`},onClick:e=>e.stopPropagation(),children:(0,n.jsx)("div",{className:"py-0.5",children:M.length>0?M.map((e=>(0,n.jsx)("button",{type:"button",className:(0,i.cn)("w-full text-left","px-4 py-1.5","text-[var(--text)] text-sm","hover:bg-[var(--active)]/20","focus:bg-[var(--active)]/30","transition-colors duration-150","font-['Space_Mono',_monospace]"),onClick:()=>{o({selectedModel:e.id}),b(""),w(!1)},children:(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsxs)("span",{children:[e.host?`(${e.host}) `:"",e.id,e.context_length&&(0,n.jsxs)("span",{className:"text-xs text-[var(--text)] opacity-50 ml-1",children:["[ctx: ",e.context_length,"]"]})]})})},e.id))):(0,n.jsx)("div",{className:"px-4 py-1.5 text-[var(--text)] opacity-50 text-sm",children:"No models found"})})})})]})]}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)(l.$,{size:"default",onClick:()=>{x(!0),t(!1)},variant:"outline",className:(0,i.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9","bg-[rgba(255,250,240,0.4)] dark:bg-[rgba(255,255,255,0.1)]","border-[var(--text)]/10","font-['Space_Mono',_monospace]","hover:border-[var(--active)] hover:brightness-98 active:bg-[var(--active)] active:brightness-95","focus:ring-1 focus:ring-[var(--active)]","mb-4"),children:"Configuration"}),(0,n.jsx)(l.$,{variant:"outline",size:"default",onClick:()=>{g(!0),t(!1)},className:(0,i.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9","bg-[rgba(255,250,240,0.4)] dark:bg-[rgba(255,255,255,0.1)]","border-[var(--text)]/10","font-['Space_Mono',_monospace]","hover:border-[var(--active)] hover:brightness-98 active:bg-[var(--active)] active:brightness-95","focus:ring-1 focus:ring-[var(--active)]","mb-4 mt-3"),children:"Chat History"})]})]})})]})]})};var g=s(77086),f=s(65634),b=s(81339),v=s(61351),w=s(56973);const y=()=>{const{config:e,updateConfig:t}=(0,o.UK)(),s=e?.persona||"default",r=e?.personas||{};return(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsxs)(d.l6,{value:s,onValueChange:e=>{t({persona:e})},children:[(0,n.jsx)(d.bq,{className:(0,i.cn)("w-auto min-w-[100px] border-none bg-transparent shadow-none","text-apple-footnote font-medium text-foreground","hover:bg-secondary/50 rounded-lg px-2 py-1 h-7","focus:ring-2 focus:ring-primary/20 focus:border-primary/50"),children:(0,n.jsx)(d.yv,{placeholder:"Select persona"})}),(0,n.jsx)(d.gC,{className:"bg-popover border border-border shadow-lg rounded-lg",children:(0,n.jsx)(m.Bc,{delayDuration:300,children:Object.keys(r).map((e=>(0,n.jsxs)(m.m_,{children:[(0,n.jsx)(m.k$,{asChild:!0,children:(0,n.jsx)(d.eb,{value:e,className:(0,i.cn)("text-apple-callout text-popover-foreground","hover:bg-accent hover:text-accent-foreground","focus:bg-accent focus:text-accent-foreground","cursor-pointer rounded-md"),children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"text-sm",children:p.rm[e]||p.rm.default}),"default"===e?"Chromepanion":e]})})}),(0,n.jsx)(m.ZI,{side:"right",className:"bg-popover text-popover-foreground border border-border max-w-xs",sideOffset:8,children:(0,n.jsx)("p",{className:"text-apple-footnote",children:p.K[e]||p.K.default})})]},e)))})})]})})};var j=s(21085);const N=()=>{const{config:e}=(0,o.UK)(),t=e?.selectedModel;return t?(0,n.jsx)(j.E,{className:(0,i.cn)("bg-primary/10 text-primary border-primary/20","text-apple-footnote font-medium px-2 py-1 h-7"),children:t}):(0,n.jsx)(j.E,{className:(0,i.cn)("bg-secondary/50 text-secondary-foreground border-border","text-apple-footnote font-medium px-2 py-1 h-7"),children:"No Model"})},C=({isOpen:e,onClose:t,setSettingsMode:s})=>(0,n.jsx)(g.lG,{open:e,onOpenChange:t,children:(0,n.jsxs)(g.Cf,{variant:"themedPanel",className:(0,i.cn)("[&>button]:hidden","bg-card border border-border shadow-lg"),style:{width:"20rem",height:"12rem",borderRadius:"var(--radius-lg)",boxShadow:"var(--shadow-lg)"},onInteractOutside:e=>e.preventDefault(),children:[(0,n.jsx)(g.c7,{className:"text-center p-4",children:(0,n.jsx)(g.L3,{className:"text-apple-title3 text-foreground",children:"Welcome to Chromepanion"})}),(0,n.jsx)(g.rr,{asChild:!0,children:(0,n.jsxs)("div",{className:"px-6 pb-6 text-center",children:[(0,n.jsx)("p",{className:"text-apple-body text-muted-foreground mb-6",children:"Get started by connecting to your AI models"}),(0,n.jsx)("div",{className:"flex justify-center",children:(0,n.jsx)(l.$,{variant:"default",className:"bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg px-6 py-2 text-apple-callout font-medium",onClick:()=>s(!0),"aria-label":"Open Settings",children:"Open Settings"})})]})})]})}),k=({isOpen:e,onOpenChange:t,config:s,updateConfig:a})=>{const[o,c]=(0,r.useState)(s?.userName||""),[d,m]=(0,r.useState)(s?.userProfile||"");(0,r.useEffect)((()=>{e&&(c(s?.userName||""),m(s?.userProfile||""))}),[e,s?.userName,s?.userProfile]);return(0,n.jsx)(g.lG,{open:e,onOpenChange:t,children:(0,n.jsxs)(g.Cf,{variant:"themedPanel",className:"max-w-xs",children:[(0,n.jsxs)(g.c7,{className:"px-6 py-4 border-b border-[var(--text)]/10",children:[(0,n.jsx)(g.L3,{className:"text-lg font-semibold text-[var(--text)]",children:"Edit Profile"}),(0,n.jsx)(g.rr,{className:"text-sm text-[var(--text)] opacity-80",children:"Set your display name and profile information. (For chat and export purposes)"})]}),(0,n.jsxs)("div",{className:"px-6 py-5 space-y-4",children:[(0,n.jsxs)("div",{className:"space-y-1.5",children:[(0,n.jsx)(f.J,{htmlFor:"username",className:"text-sm font-medium text-[var(--text)] opacity-90",children:"Username"}),(0,n.jsx)(u.p,{id:"username",value:o,onChange:e=>c(e.target.value),className:(0,i.cn)("focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98")})]}),(0,n.jsxs)("div",{className:"space-y-1.5",children:[(0,n.jsx)(f.J,{htmlFor:"userprofile",className:"text-sm font-medium text-[var(--text)] opacity-90",children:"User Profile"}),(0,n.jsx)(u.p,{id:"userprofile",value:d,onChange:e=>m(e.target.value),className:(0,i.cn)("focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98")})]})]}),(0,n.jsxs)(g.Es,{className:"px-6 py-4 border-t border-[var(--text)]/10",children:[(0,n.jsx)(l.$,{variant:"outline-subtle",size:"sm",onClick:()=>t(!1),children:"Cancel"}),(0,n.jsx)(l.$,{variant:"active-bordered",size:"sm",onClick:()=>{a({userName:o,userProfile:d}),t(!1)},children:"Save Changes"})]})]})})},S=({chatTitle:e,settingsMode:t,setSettingsMode:s,historyMode:c,setHistoryMode:d,deleteAll:u,reset:h,downloadImage:p,downloadJson:f,downloadText:j,downloadMarkdown:S})=>{const{config:$,updateConfig:M}=(0,o.UK)(),[E,A]=(0,r.useState)(!1),[P,T]=(0,r.useState)(!1),L=e&&!t&&!c,[z,O]=(0,r.useState)(!1),I=t||c,_=I?"Back to Chat":"",F="z-50 min-w-[8rem] overflow-hidden rounded-lg border bg-popover p-1 text-popover-foreground shadow-lg animate-in data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",U="flex cursor-default select-none items-center rounded-md px-3 py-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",q="-mx-1 my-1 h-px bg-border";return(0,n.jsx)(m.Bc,{delayDuration:500,children:(0,n.jsxs)("div",{className:(0,i.cn)("bg-background/95 backdrop-blur-sm text-foreground","border-b border-border","sticky top-0 z-10"),children:[(0,n.jsxs)("div",{className:"flex items-center h-auto py-3 px-4",children:[(0,n.jsx)("div",{className:"flex justify-start items-center min-h-10 gap-3",children:I?(0,n.jsxs)(m.m_,{children:[(0,n.jsx)(m.k$,{asChild:!0,children:(0,n.jsx)(l.$,{"aria-label":_,variant:"ghost",size:"sm",className:"text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center",onClick:()=>{I&&(s(!1),d(!1))},children:(0,n.jsx)(a.yGN,{size:"18px"})})}),(0,n.jsx)(m.ZI,{side:"bottom",className:"bg-popover text-popover-foreground border border-border",children:_})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(y,{}),(0,n.jsx)(N,{})]})}),(0,n.jsxs)("div",{className:"flex-grow flex justify-center items-center overflow-hidden px-4",children:[L&&(0,n.jsx)("p",{className:"text-apple-headline text-foreground whitespace-nowrap overflow-hidden text-ellipsis text-center",children:e}),t&&(0,n.jsx)("div",{className:"flex items-center justify-center",children:(0,n.jsx)("p",{className:"text-apple-title3 text-foreground",children:"Settings"})}),c&&(0,n.jsx)("div",{className:"flex items-center justify-center",children:(0,n.jsx)("p",{className:"text-apple-title3 text-foreground",children:"Chat History"})})]}),(0,n.jsxs)("div",{className:"flex justify-end items-center min-h-10 gap-2",children:[!t&&!c&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(m.m_,{children:[(0,n.jsx)(m.k$,{asChild:!0,children:(0,n.jsx)(l.$,{"aria-label":"Reset Chat",variant:"ghost",size:"sm",className:"text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center group",onClick:h,children:(0,n.jsx)(a.yPB,{size:"16px",className:"transition-transform duration-300 rotate-0 group-hover:rotate-180"})})}),(0,n.jsx)(m.ZI,{side:"bottom",className:"bg-popover text-popover-foreground border border-border",children:"Reset Chat"})]}),(0,n.jsxs)(m.m_,{children:[(0,n.jsx)(m.k$,{asChild:!0,children:(0,n.jsx)(l.$,{"aria-label":"Chat History",variant:"ghost",size:"sm",className:"text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center",onClick:()=>{d(!0)},children:(0,n.jsx)(a.Ohp,{size:"16px"})})}),(0,n.jsx)(m.ZI,{side:"bottom",className:"bg-popover text-popover-foreground border border-border",children:"Chat History"})]}),(0,n.jsxs)(b.bL,{children:[(0,n.jsxs)(m.m_,{children:[(0,n.jsx)(m.k$,{asChild:!0,children:(0,n.jsx)(b.l9,{asChild:!0,children:(0,n.jsx)(l.$,{"aria-label":"More Options",variant:"ghost",size:"sm",className:"text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center",children:(0,n.jsx)(a.$Ri,{size:"16px"})})})}),(0,n.jsx)(m.ZI,{side:"bottom",className:"bg-popover text-popover-foreground border border-border",children:"More Options"})]}),(0,n.jsx)(b.ZL,{children:(0,n.jsxs)(b.UC,{className:(0,i.cn)(F,"bg-popover text-popover-foreground border border-border shadow-xl min-w-[180px]"),sideOffset:5,align:"end",children:[(0,n.jsx)(b.q7,{className:(0,i.cn)(U,"hover:bg-accent hover:text-accent-foreground cursor-pointer"),onSelect:()=>{M({theme:"dark"===($?.theme||"light")?"light":"dark"})},children:(0,n.jsxs)("div",{className:"flex items-center gap-3 w-full",children:["dark"===$?.theme?(0,n.jsx)(a.Wh$,{className:"h-4 w-4"}):(0,n.jsx)(a.hkc,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"Theme"}),(0,n.jsx)("span",{className:"ml-auto text-xs text-muted-foreground",children:"dark"===$?.theme?"Light":"Dark"})]})}),(0,n.jsx)(b.q7,{className:(0,i.cn)(U,"hover:bg-accent hover:text-accent-foreground cursor-pointer"),onSelect:()=>s(!0),children:(0,n.jsxs)("div",{className:"flex items-center gap-3 w-full",children:[(0,n.jsx)(a.VSk,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"Settings"})]})}),(0,n.jsx)(b.wv,{className:(0,i.cn)(q,"bg-border")}),(0,n.jsxs)(b.Pb,{children:[(0,n.jsx)(b.ZP,{className:(0,i.cn)("flex cursor-default select-none items-center rounded-md px-3 py-2 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent","hover:bg-accent hover:text-accent-foreground cursor-pointer"),children:(0,n.jsxs)("div",{className:"flex items-center gap-3 w-full",children:[(0,n.jsx)(a.pdY,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"Share Options"}),(0,n.jsx)(a.irw,{className:"ml-auto h-4 w-4 rotate-180"})]})}),(0,n.jsx)(b.ZL,{children:(0,n.jsxs)(b.G5,{className:(0,i.cn)(F,"bg-popover text-popover-foreground border border-border shadow-lg"),sideOffset:2,alignOffset:-5,children:[(0,n.jsx)(b.q7,{className:(0,i.cn)(U,"hover:bg-accent hover:text-accent-foreground cursor-pointer"),onSelect:()=>A(!0),children:(0,n.jsxs)("div",{className:"flex items-center gap-3 w-full",children:[(0,n.jsx)(v.uSr,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"Edit Profile"})]})}),(0,n.jsx)(b.wv,{className:(0,i.cn)(q,"bg-border")}),(0,n.jsx)(b.q7,{className:(0,i.cn)(U,"hover:bg-accent hover:text-accent-foreground cursor-pointer"),onSelect:S,children:(0,n.jsxs)("div",{className:"flex items-center gap-3 w-full",children:[(0,n.jsx)(w.nR3,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"Export as Markdown"})]})}),(0,n.jsx)(b.q7,{className:(0,i.cn)(U,"hover:bg-accent hover:text-accent-foreground cursor-pointer"),onSelect:j,children:(0,n.jsxs)("div",{className:"flex items-center gap-3 w-full",children:[(0,n.jsx)(v.mup,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"Export as Text"})]})}),(0,n.jsx)(b.q7,{className:(0,i.cn)(U,"hover:bg-accent hover:text-accent-foreground cursor-pointer"),onSelect:f,children:(0,n.jsxs)("div",{className:"flex items-center gap-3 w-full",children:[(0,n.jsx)(a.dG_,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"Export as JSON"})]})}),(0,n.jsx)(b.q7,{className:(0,i.cn)(U,"hover:bg-accent hover:text-accent-foreground cursor-pointer"),onSelect:p,children:(0,n.jsxs)("div",{className:"flex items-center gap-3 w-full",children:[(0,n.jsx)(v.Af8,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"Export as Image"})]})})]})})]}),(0,n.jsx)(b.wv,{className:(0,i.cn)(q,"bg-border")}),(0,n.jsx)(b.q7,{className:(0,i.cn)(U,"opacity-50 cursor-not-allowed"),disabled:!0,children:(0,n.jsxs)("div",{className:"flex items-center gap-3 w-full",children:[(0,n.jsx)(a.pcC,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"Privacy"}),(0,n.jsx)("span",{className:"ml-auto text-xs text-muted-foreground",children:"Soon"})]})}),(0,n.jsx)(b.q7,{className:(0,i.cn)(U,"opacity-50 cursor-not-allowed"),disabled:!0,children:(0,n.jsxs)("div",{className:"flex items-center gap-3 w-full",children:[(0,n.jsx)(a.S8s,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"About"}),(0,n.jsx)("span",{className:"ml-auto text-xs text-muted-foreground",children:"Soon"})]})})]})})]})]}),c&&(0,n.jsxs)(m.m_,{children:[(0,n.jsx)(m.k$,{asChild:!0,children:(0,n.jsx)(l.$,{"aria-label":"Delete All History",variant:"ghost",size:"sm",className:"text-[var(--text)] rounded-md",onClick:()=>{T(!0)},children:(0,n.jsx)(a.IXo,{size:"18px"})})}),(0,n.jsx)(m.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:"Delete All"})]})]})]}),(!$?.models||0===$.models.length)&&!t&&!c&&(0,n.jsx)(C,{isOpen:!0,setSettingsMode:s,onClose:()=>{}}),(0,n.jsx)(x,{isOpen:z,onOpenChange:e=>{O(e)},config:$,updateConfig:M,setSettingsMode:s,setHistoryMode:d}),(0,n.jsx)(k,{isOpen:E,onOpenChange:A,config:$,updateConfig:M}),(0,n.jsx)(g.lG,{open:P,onOpenChange:T,children:(0,n.jsxs)(g.Cf,{variant:"themedPanel",className:"max-w-sm",children:[(0,n.jsxs)(g.c7,{className:"px-6 py-4 border-b border-[var(--text)]/10",children:[(0,n.jsx)(g.L3,{className:"text-lg font-semibold text-[var(--text)]",children:"Confirm Deletion"}),(0,n.jsx)(g.rr,{className:"text-sm text-[var(--text)] opacity-90",children:"Are you sure you want to delete all chat history? This action cannot be undone."})]}),(0,n.jsxs)(g.Es,{className:"px-6 py-4 border-t border-[var(--text)]/10",children:[(0,n.jsx)(l.$,{variant:"outline-subtle",size:"sm",onClick:()=>T(!1),children:"Cancel"}),(0,n.jsx)(l.$,{variant:"destructive",size:"sm",onClick:async()=>{try{"function"==typeof u&&await u()}catch(e){}finally{T(!1)}},children:"Delete All"})]})]})})]})})}},59853:(e,t,s)=>{s.d(t,{S:()=>l});var n=s(96540),r=s(36948),a=s(82951);const o=e=>{const t=e.replace(/^(what\s+(is|are|was|were|do|does|did|can|could|would|will|should)\s+)/i,"").replace(/^(how\s+(do|does|did|can|could|would|will|should|to)\s+)/i,"").replace(/^(can\s+you\s+)/i,"").replace(/^(could\s+you\s+)/i,"").replace(/^(would\s+you\s+)/i,"").replace(/^(please\s+)/i,"").replace(/^(help\s+me\s+)/i,"").replace(/^(tell\s+me\s+)/i,"").replace(/^(explain\s+)/i,"").replace(/^(show\s+me\s+)/i,"").replace(/\?+$/,"").trim().split(/\s+/).filter((e=>e.length>2));return t.slice(0,Math.min(6,Math.max(3,t.length))).map((e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase())).join(" ")},i=(e,t="")=>{let s=e.replace(/<think>[\s\S]*?<\/think>/g,"").replace(/["'`]/g,"").replace(/#/g,"").replace(/^\s*title:\s*/i,"").replace(/^\s*-\s*/,"").trim();if(!s||s.length<3)return t?o(t):"New Chat";const n=s.split(/\s+/).filter((e=>{const t=e.toLowerCase().replace(/[^\w]/g,"");return t.length>1&&!["the","and","or","but","in","on","at","to","for","of","with","by"].includes(t)}));return n.slice(0,Math.min(6,Math.max(3,n.length))).map(((e,t)=>{const s=e.replace(/[^\w]/g,"");return 0===t||s.length>3?s.charAt(0).toUpperCase()+s.slice(1).toLowerCase():s.toLowerCase()})).join(" ")},l=(e,t,s)=>{const[l,c]=(0,n.useState)(""),{config:d}=(0,r.UK)(),u=(0,n.useRef)(null);return(0,n.useEffect)((()=>{if(!e&&t.length>=2&&!l&&d?.generateTitle){u.current&&u.current.abort(),u.current=new AbortController;const e=u.current.signal,s=d?.models?.find((e=>e.id===d.selectedModel));if(!s)return;const n=t.find((e=>"user"===e.role))?.rawContent||"",r=[{role:"system",content:'You are a title generator. Create concise, descriptive titles (3-6 words) for chat conversations based on the user\'s first message.\n\nRules:\n- Remove question words like "what is", "how do", "can you", "please", etc.\n- Focus on the main topic or subject\n- Use title case (capitalize important words)\n- Be specific and searchable\n- No quotes, no explanations, just the title\n\nExamples:\n"What is the reason for the current riots in New York?" → "New York Riots Reason"\n"How do I install Docker on Ubuntu?" → "Install Docker Ubuntu"\n"Can you explain quantum computing?" → "Quantum Computing Explanation"\n"Please help me debug this Python code" → "Python Code Debugging"\n"Tell me about machine learning algorithms" → "Machine Learning Algorithms"\n"What are the best restaurants in Paris?" → "Best Paris Restaurants"'},{role:"user",content:`Create a title for this message: "${n}"`}],l=(()=>{const e={body:{model:s.id,messages:r,stream:!["ollama"].includes(s.host||"")},headers:{}};if("ollama"===s.host)return{...e,url:`${d.ollamaUrl}/api/chat`}})();if(!l)return;const m=t=>{e.aborted};if(["ollama"].includes(s.host||""))fetch(l.url,{method:"POST",headers:{"Content-Type":"application/json",...l.headers},body:JSON.stringify(l.body),signal:e}).then((e=>e.json())).then((e=>{const t=e.message?.content||"",s=i(t,n);if(s&&"New Chat"!==s)c(s);else{const e=o(n);c(e)}})).catch((e=>{m(e);const t=o(n);c(t)}));else{let t="";(0,a.hL)(l.url,l.body,((s,r)=>{if(t=s,!e.aborted&&r){const e=i(t,n);if(e&&"New Chat"!==e)c(e);else{const e=o(n);c(e)}}}),l.headers,s.host||"",e).catch((e=>{m(e);const t=o(n);c(t)}))}}return()=>{u.current&&(u.current.abort(),u.current=null)}}),[e,t,s,d,l]),{chatTitle:l,setChatTitle:c}}},60523:(e,t,s)=>{s.d(t,{V:()=>i});var n=s(74848),r=s(45284),a=s(36948),o=s(67520);const i=()=>{const{config:e}=(0,a.UK)(),t=e?.persona||"default",s=o.z$[t]||o.z$.default,i=(0,r.cn)("flex","items-center","justify-center","h-full","fixed","w-full","top-[10%]","pointer-events-none"),l=(0,r.cn)("fixed","opacity-[0.03]","z-[1]");return(0,n.jsx)("div",{className:i,children:(0,n.jsx)("img",{src:s,alt:"",className:l,style:{zoom:"1.2"}})})}},62570:()=>{},67520:(e,t,s)=>{s.d(t,{K:()=>r,rm:()=>n,z$:()=>a});const n={Scholar:"🎓",Executive:"💼",Storyteller:"📚",Skeptic:"🤔",Mentor:"🧭",Investigator:"🔍",Pragmatist:"⚙️",Enthusiast:"⚡",Curator:"🎨",Friend:"😊",default:"🤖"},r={Scholar:"Analytical academic researcher with formal tone. Provides structured analysis with citations and source credibility assessment.",Executive:"Strategic business leader with direct, results-oriented tone. Delivers concise insights with actionable recommendations.",Storyteller:"Engaging narrative creator with vivid language. Weaves information into compelling stories using metaphors and examples.",Skeptic:"Critical analyst with questioning tone. Highlights biases, contradictions, and missing information in sources.",Mentor:"Educational guide with encouraging tone. Breaks down complex topics with step-by-step explanations and learning tips.",Investigator:"Methodical fact-checker with systematic approach. Verifies information credibility and distinguishes facts from claims.",Pragmatist:"Solution-focused analyst with practical tone. Emphasizes actionable insights and real-world implementation strategies.",Enthusiast:"Energetic discoverer with exciting tone. Highlights fascinating developments and breakthrough insights with enthusiasm.",Curator:"Sophisticated synthesizer with refined tone. Provides premium insights with elegant analysis and polished presentation.",Friend:"Casual conversationalist with friendly tone. Shares discoveries in approachable, relatable manner like talking to a friend.",default:"AI assistant ready to help with your search and analysis needs."},a={Scholar:"assets/images/chromepanion.png",Executive:"assets/images/chromepanion.png",Storyteller:"assets/images/chromepanion.png",Skeptic:"assets/images/chromepanion.png",Mentor:"assets/images/chromepanion.png",Investigator:"assets/images/chromepanion.png",Pragmatist:"assets/images/chromepanion.png",Enthusiast:"assets/images/chromepanion.png",Curator:"assets/images/chromepanion.png",Friend:"assets/images/chromepanion.png",default:"assets/images/chromepanion.png"}},73968:(e,t,s)=>{s.d(t,{p:()=>u});var n=s(74848),r=s(96540),a=s(56973),o=s(36948),i=s(32090),l=s(83885),c=s(89696),d=s(45284);const u=({isLoading:e,message:t,setMessage:s,onSend:u,onStopRequest:m})=>{const{config:h}=(0,o.UK)(),p=(0,r.useRef)(null),[x,g]=(0,r.useState)(!1);(0,r.useEffect)((()=>{p.current?.focus()}),[t,h?.chatMode]);return(0,n.jsx)("div",{className:"flex flex-col gap-3 mb-4",children:(0,n.jsxs)("div",{className:(0,d.cn)("flex w-full border border-border items-center gap-2 p-3 bg-card rounded-xl shadow-sm",x&&"ring-2 ring-primary/20 border-primary/50"),children:[(0,n.jsx)(c.T,{autosize:!0,ref:p,minRows:1,maxRows:8,autoComplete:"off",id:"user-input",placeholder:"Search the web with AI...",value:t,autoFocus:!0,onChange:e=>s(e.target.value),onKeyDown:s=>{e||"Enter"!==s.key||!t.trim()||s.altKey||s.metaKey||s.shiftKey||(s.preventDefault(),s.stopPropagation(),u())},className:"flex-grow bg-transparent border-none shadow-none outline-none focus-visible:ring-0 resize-none text-apple-body",onFocus:()=>g(!0),onBlur:()=>g(!1)}),(0,n.jsx)("div",{className:"flex items-center gap-2",children:(0,n.jsx)(l.Bc,{delayDuration:300,children:(0,n.jsxs)(l.m_,{children:[(0,n.jsx)(l.k$,{asChild:!0,children:(0,n.jsx)(i.$,{"aria-label":"Send",variant:"ghost",size:"sm",className:(0,d.cn)("p-2 rounded-lg h-8 w-8 flex items-center justify-center",e?"text-destructive hover:bg-destructive/10":"text-primary hover:bg-primary/10",!e&&!t.trim()&&"opacity-50"),onClick:s=>{s.stopPropagation(),e?m():t.trim()&&u()},disabled:!e&&!t.trim(),children:e?(0,n.jsx)(a.wO6,{className:"h-4 w-4"}):(0,n.jsx)(a.B07,{className:"h-4 w-4"})})}),(0,n.jsx)(l.ZI,{side:"top",className:"bg-popover text-popover-foreground border border-border",children:(0,n.jsx)("p",{className:"text-apple-footnote",children:e?"Stop":"Send"})})]})})})]})})}},78473:(e,t,s)=>{s.d(t,{D:()=>p});var n=s(74848),r=s(96540),a=s(31301),o=s(32090),i=s(4539),l=s(16250),c=s(73790),d=s.n(c),u=s(26532);const m=e=>new Date(e).toLocaleDateString("sv-SE"),h=12,p=({loadChat:e,onDeleteAll:t,className:s})=>{const[c,p]=(0,r.useState)([]),[x,g]=(0,r.useState)(""),[f,b]=(0,r.useState)(1),[v,w]=(0,r.useState)(null),[y,j]=(0,r.useState)(null),N=(0,r.useCallback)((e=>{const t=e.sort(((e,t)=>t.last_updated-e.last_updated));p(t)}),[]);(0,r.useEffect)((()=>{(async()=>{try{const e=(await d().keys()).filter((e=>e.startsWith("chat_")));if(0===e.length)return p([]),void b(1);const t=e.map((e=>d().getItem(e))),s=(await Promise.all(t)).filter((e=>null!==e&&"object"==typeof e&&"id"in e&&"last_updated"in e));N(s),b(1)}catch(e){p([])}})()}),[N]);const C=(0,r.useMemo)((()=>{if(!x)return c;const e=x.toLowerCase();return c.filter((t=>{const s=t.title?.toLowerCase().includes(e),n=t.turns.some((t=>t.rawContent.toLowerCase().includes(e)));return s||n}))}),[c,x]);(0,r.useEffect)((()=>{b(1)}),[x]);const k=(0,r.useMemo)((()=>Math.max(1,Math.ceil(C.length/h))),[C]);(0,r.useEffect)((()=>{f>k&&b(k)}),[f,k]);const S=(0,r.useMemo)((()=>{const e=(f-1)*h,t=e+h;return C.slice(e,t)}),[C,f]),$=(0,r.useMemo)((()=>S.map((e=>({...e,date:m(e.last_updated)})))),[S]),M=(0,r.useMemo)((()=>Array.from(new Set($.map((e=>e.date))))),[$]),E=(0,r.useCallback)((async e=>{try{await d().removeItem(e);const t=(await d().keys()).filter((e=>e.startsWith("chat_"))),s=(await Promise.all(t.map((e=>d().getItem(e))))).filter((e=>e&&"object"==typeof e&&"id"in e&&"last_updated"in e&&"turns"in e));N(s);const n=s.filter((e=>{if(!x)return!0;const t=x.toLowerCase(),s=e.title?.toLowerCase().includes(t),n=e.turns.some((e=>e.rawContent.toLowerCase().includes(t)));return s||n})),r=Math.max(1,Math.ceil(n.length/h));let a=f;a>r&&(a=r);const o=(a-1)*h;0===n.slice(o,o+h).length&&a>1&&(a-=1),b(a)}catch(t){}}),[N,f,x]),A=(0,r.useCallback)((async()=>{try{const e=(await d().keys()).filter((e=>e.startsWith("chat_")));await Promise.all(e.map((e=>d().removeItem(e)))),p([]),t&&t()}catch(e){}}),[t]);(0,r.useEffect)((()=>(window.deleteAllChats=A,()=>{window.deleteAllChats===A&&delete window.deleteAllChats})),[A]);const P=(0,r.useCallback)((()=>b((e=>Math.min(e+1,k)))),[k]),T=(0,r.useCallback)((()=>b((e=>Math.max(e-1,1)))),[]),L=`flex flex-col w-full ${s||""}`.trim(),z=e=>{g(e.target.value)};return 0!==c.length||x?0===C.length&&x?(0,n.jsxs)("div",{className:L,children:[(0,n.jsx)("div",{className:"p-0",children:(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)(u.p,{type:"text",placeholder:"Search chat history (titles & content)...",value:x,onChange:z,className:"w-full bg-background rounded-none text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10"}),(0,n.jsx)(l.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,n.jsx)(i.F,{className:"flex-1 w-full min-h-0",children:(0,n.jsxs)("div",{className:"px-4 pb-4 pt-5 text-center font-['Space_Mono',_monospace] text-foreground/70 h-full flex items-center justify-center",children:['No results found for "',x,'".']})})]}):(0,n.jsxs)("div",{className:L,children:[(0,n.jsx)("div",{className:"p-0",children:(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)(u.p,{type:"text",placeholder:"Search chat history (titles & content)...",value:x,onChange:z,className:"w-full bg-background rounded-none text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10"}),(0,n.jsx)(l.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,n.jsx)(i.F,{className:"flex-1 w-full min-h-0",children:(0,n.jsx)("div",{className:"px-4 pb-4 font-['Space_Mono',_monospace]",children:M.map((t=>(0,n.jsxs)("div",{className:"mb-3 mt-3",children:[(0,n.jsx)("p",{className:"text-foreground text-lg font-bold overflow-hidden pl-4 pb-1 text-left text-ellipsis whitespace-nowrap w-[90%]",children:t===m(new Date)?"Today":t}),$.filter((e=>e.date===t)).map((t=>(0,n.jsxs)("div",{className:"flex items-center group font-['Space_Mono',_monospace]",onMouseEnter:()=>w(t.id),onMouseLeave:()=>w(null),children:[(0,n.jsxs)("span",{className:"text-foreground text-base font-normal pl-4 w-[4.5rem] flex-shrink-0 font-['Space_Mono',_monospace]",children:[new Date(t.last_updated).getHours().toString().padStart(2,"0"),":",new Date(t.last_updated).getMinutes().toString().padStart(2,"0")]}),(0,n.jsx)("button",{className:`text-foreground text-base font-normal overflow-hidden px-4 py-2 text-left text-ellipsis whitespace-nowrap flex-grow hover:underline hover:underline-offset-4 hover:decoration-1 ${t.id===y?"line-through decoration-2":""} font-['Space_Mono',_monospace]`,onClick:()=>e(t),children:t.title||"Untitled Chat"}),(0,n.jsx)(a.PY1.div,{className:"shrink-0 transition-opacity duration-150 "+(v===t.id?"opacity-100":"opacity-0 group-hover:opacity-100"),whileHover:{rotate:"15deg"},onMouseEnter:()=>j(t.id),onMouseLeave:()=>j(null),children:(0,n.jsx)(o.$,{variant:"ghost",size:"sm","aria-label":"Delete chat",className:"rounded-full w-8 h-8 font-['Space_Mono',_monospace]",onClick:e=>{e.stopPropagation(),E(t.id)},children:(0,n.jsx)(l.ttk,{className:"h-4 w-4 text-foreground"})})})]},t.id)))]},t)))})}),k>1&&(0,n.jsxs)("div",{className:"flex justify-center items-center h-10 space-x-2 p-2 border-t border-[var(--active)]/50 font-['Space_Mono',_monospace]",children:[(0,n.jsx)(o.$,{onClick:T,disabled:1===f,variant:"ghost",className:"font-['Space_Mono',_monospace]",children:"Prev"}),(0,n.jsxs)("span",{className:"text-md",children:["Page ",f," of ",k]}),(0,n.jsx)(o.$,{onClick:P,disabled:f===k,variant:"ghost",className:"font-['Space_Mono',_monospace]",children:"Next"})]})]}):(0,n.jsxs)("div",{className:L,children:[(0,n.jsx)("div",{className:"p-0",children:(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)(u.p,{type:"text",placeholder:"Search chat history (titles & content)...",value:x,onChange:z,className:"w-full bg-background rounded-none text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10"}),(0,n.jsx)(l.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,n.jsx)(i.F,{className:"flex-1 w-full min-h-0",children:(0,n.jsx)("div",{className:"px-4 pb-4 pt-5 text-center font-['Space_Mono',_monospace] text-foreground/70 h-full flex items-center justify-center",children:"No chat history found."})})]})}},82951:(e,t,s)=>{s.d(t,{hL:()=>i,GW:()=>n,hj:()=>l,tE:()=>o});const n=async(e,t,s,n,r,a=[],o)=>{try{if(!s?.host)return e;const i=`You are a Google search query optimizer. Your task is to rewrite user's input [The user's raw input && chat history:${a.map((e=>`{{${e.role}}}: ${e.content}`)).join("\n")}].\n\n\nInstructions:\n**Important** No Explanation, just the optimized query!\n\n\n1. Extract the key keywords and named entities from the user's input.\n2. Correct any obvious spelling errors.\n3. Remove unnecessary words (stop words) unless they are essential for the query's meaning.\n4. If the input is nonsensical or not a query, return the original input.\n5. Using previous chat history to understand the user's intent.\n\n\nOutput:\n'The optimized Google search query'\n\n\nExample 1:\nInput from user ({{user}}): where can i find cheep flights to london\nOutput:\n'cheap flights London'\n\n\nExample 2:\nContext: {{user}}:today is a nice day in paris i want to have a walk and find a restaurant to have a nice meal. {{assistant}}: Bonjour, it's a nice day!\nInput from user ({{user}}): please choose me the best restarant\nOutput:\n'best restaurants Paris France'\n\n\nExample 3:\nInput from user ({{user}}): asdf;lkjasdf\nOutput:\n'asdf;lkjasdf'\n`,l={ollama:`${t?.ollamaUrl||""}/api/chat`}[s.host];if(!l)return e;const c={model:t?.selectedModel||s.id||"",messages:[{role:"system",content:i},{role:"user",content:e}],stream:!1};let d;void 0!==o?d=o:void 0!==t.temperature&&(d=t.temperature),void 0!==d&&(c.temperature=d);const u=await fetch(l,{method:"POST",headers:{"Content-Type":"application/json",...n||{}},signal:r,body:JSON.stringify(c)});if(!u.ok){await u.text();throw new Error(`API request failed: ${u.statusText}`)}const m=await u.json(),h=m?.choices?.[0]?.message?.content;return"string"==typeof h?(e=>e.replace(/<think>[\s\S]*?<\/think>/g,"").replace(/["']/g,"").trim())(h):e}catch(i){if(r?.aborted||i instanceof Error&&"AbortError"===i.name)throw i;return e}},r=async function(e){try{const t=new URL(e);if("chrome:"===t.protocol)return;const s=[t.hostname],n=[{id:1,priority:1,condition:{requestDomains:s},action:{type:"modifyHeaders",requestHeaders:[{header:"Origin",operation:"set",value:`${t.protocol}//${t.hostname}`}]}}];await chrome.declarativeNetRequest.updateDynamicRules({removeRuleIds:n.map((e=>e.id)),addRules:n})}catch(t){}},a=e=>{try{const t=(new DOMParser).parseFromString(e,"text/html");t.querySelectorAll('script, style, nav, footer, header, svg, img, noscript, iframe, form, aside, .sidebar, .ad, .advertisement, .banner, .popup, .modal, .cookie-banner, link[rel="stylesheet"], button, input, select, textarea, [role="navigation"], [role="banner"], [role="contentinfo"], [aria-hidden="true"]').forEach((e=>e.remove()));let s=t.querySelector("main")||t.querySelector("article")||t.querySelector(".content")||t.querySelector("#content")||t.querySelector(".main-content")||t.querySelector("#main-content")||t.querySelector(".post-content")||t.body,n=s?.textContent||"";return n=n.replace(/\s+/g," ").trim(),n=n.split("\n").filter((e=>e.trim().length>20)).join("\n"),n}catch(t){return"[Error extracting content]"}},o=async(e,t,s)=>{const n=t.webMode,r=t.serpMaxLinksToVisit??3,o=t.webLimit&&128!==t.webLimit?1e3*t.webLimit:1/0,i=s||(new AbortController).signal;if(!n)return"Error: Web search mode is undefined. Please check your configuration.";try{if("Google"===n){const t=new AbortController,s=setTimeout((()=>{t.abort()}),15e3),l="function"==typeof AbortSignal.any?AbortSignal.any([i,t.signal]):i,c=`https://www.google.com/search?q=${encodeURIComponent(e)}&hl=en&gl=us&num=10&start=0&safe=off&filter=0`,d=await fetch(c,{signal:l,method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",Accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","Accept-Language":"en-US,en;q=0.9","Accept-Encoding":"gzip, deflate, br",DNT:"1",Connection:"keep-alive","Upgrade-Insecure-Requests":"1","Sec-Fetch-Dest":"document","Sec-Fetch-Mode":"navigate","Sec-Fetch-Site":"none","Sec-Fetch-User":"?1","Cache-Control":"max-age=0",Referer:"https://www.google.com/"}}).finally((()=>{clearTimeout(s)}));if(!d.ok){await d.text();throw 429===d.status?new Error("Google search rate limited (429). Please try again later."):403===d.status?new Error("Google search access forbidden (403). Google may be blocking automated requests."):d.status>=400&&d.status<500?new Error(`Google search client error (${d.status}): ${d.statusText}`):new Error(`Google search server error (${d.status}): ${d.statusText}`)}if(i.aborted)throw new Error("Web search operation aborted.");const u=await d.text();if(u.includes("captcha")||u.includes("unusual traffic")||u.includes("blocked"))return"Error: Google search may be blocked. The page contains CAPTCHA or blocking content. Please try again later.";const m=(new DOMParser).parseFromString(u,"text/html"),h=[],p=["div.g","div.MjjYud","div.hlcw0c","div.kvH3mc","div.tF2Cxc","div.yuRUbf"];let x=[];for(const e of p){const t=Array.from(m.querySelectorAll(e));if(t.length>0){x=t;break}}if(0===x.length&&(x=Array.from(m.querySelectorAll("div[data-ved], div[data-hveid]"))),x.forEach(((e,t)=>{try{let t=e.querySelector('a[href^="http"]')||e.querySelector('a[href^="/url?q="]')||e.querySelector("a[href]"),s=t?.getAttribute("href");if(s&&s.startsWith("/url?q=")){const e=new URLSearchParams(s.substring(6));s=e.get("q")||s}const n=e.querySelector("h3")||e.querySelector("h2")||e.querySelector('[role="heading"]')||e.querySelector("a[href] > div"),r=n?.textContent?.trim()||"";let a="";const o=['div[style*="-webkit-line-clamp"]','div[data-sncf="1"]',".VwiC3b span",".MUxGbd span",".s3v9rd",".st",'span[style*="-webkit-line-clamp"]'];for(const i of o){const t=e.querySelectorAll(i);if(t.length>0){a=Array.from(t).map((e=>e.textContent)).join(" ").replace(/\s+/g," ").trim();break}}if(!a&&r){const t=e.textContent||"",s=t.indexOf(r);-1!==s&&(a=t.substring(s+r.length).replace(/\s+/g," ").trim().substring(0,300))}r&&s&&s.startsWith("http")&&!s.includes("google.com/search")&&h.push({title:r,snippet:a,url:s})}catch(s){}})),0===h.length){const e=m.body?.textContent?.toLowerCase()||"";return e.includes("captcha")||e.includes("unusual traffic")||e.includes("blocked")||e.includes("robot")?"Error: Google search appears to be blocked. The page contains CAPTCHA or blocking content. Please try again later or check if the extension has proper permissions.":"No search results found. Google may have changed their page structure or the search was unsuccessful."}const g=h.slice(0,r).filter((e=>e.url)),f=g.map((async e=>{if(!e.url)return{...e,content:"[Invalid URL]",status:"error"};if(i.aborted)return{...e,content:`[Fetching aborted by user: ${e.url}]`,status:"aborted"};const t=new AbortController,s=setTimeout((()=>{t.abort()}),12e3),n="function"==typeof AbortSignal.any?AbortSignal.any([i,t.signal]):i;let r=`[Error fetching/processing: Unknown error for ${e.url}]`,o="error";try{const t=await fetch(e.url,{signal:n,method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",Accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8","Accept-Language":"en-US,en;q=0.9","Accept-Encoding":"gzip, deflate, br",DNT:"1",Connection:"keep-alive","Upgrade-Insecure-Requests":"1","Sec-Fetch-Dest":"document","Sec-Fetch-Mode":"navigate","Sec-Fetch-Site":"cross-site","Cache-Control":"max-age=0"}});if(!t.ok)throw new Error(`Failed to fetch ${e.url} - Status: ${t.status}`);const s=t.headers.get("content-type");if(!s||!s.includes("text/html"))throw new Error(`Skipping non-HTML content (${s}) from ${e.url}`);if(i.aborted)throw new Error("Web search operation aborted by user.");const l=await t.text();r=a(l),o="success"}catch(l){if("AbortError"===l.name){if(i.aborted)throw l;r=t.signal.aborted?`[Timeout fetching: ${e.url}]`:`[Fetching aborted: ${e.url}]`,o="aborted"}else r=`[Error fetching/processing: ${l.message}]`,o="error"}finally{clearTimeout(s)}return{...e,content:r,status:o}})),b=await Promise.allSettled(f);if(i.aborted)throw new Error("Web search operation aborted.");let v=`Search results for "${e}" using ${n}:\n\n`,w=0;return h.forEach(((e,t)=>{if(v+=`[Result ${t+1}: ${e.title}]\n`,v+=`URL: ${e.url||"[No URL Found]"}\n`,v+=`Snippet: ${e.snippet||"[No Snippet]"}\n`,t<g.length){const t=b[w];if("fulfilled"===t?.status){const s=t.value;if(s.url===e.url){const e=s.content.substring(0,o);v+=`Content:\n${e}${s.content.length>o?"...":""}\n\n`}else v+=`Content: [Content fetch mismatch - data for ${s.url} found, expected ${e.url}]\n\n`}else v+="rejected"===t?.status?`Content: [Error fetching: ${t.reason}]\n\n`:"Content: [Fetch status unknown]\n\n";w++}else v+="Content: [Not fetched due to link limit]\n\n"})),v.trim()}return`Unsupported web search mode: ${n}`}catch(l){if("AbortError"===l.name&&i.aborted)throw l;return`Error performing web search: ${l.message}`}};async function i(e,t,s,n={},a,o){let i=!1;const l=(e,t=!1)=>{if(!i){let n;i=!0,n="string"==typeof e?e:e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:String(e),s(n,!0,t)}},c=()=>{if(o?.aborted)throw new Error("Streaming operation aborted by user.")};if(!e.startsWith("chrome://")){e.includes("localhost")&&await r((e=>e.endsWith("/")?e.slice(0,-1):e)(e));try{const r=await fetch(e,{method:"POST",headers:{"Content-Type":"application/json",...n},body:JSON.stringify(t),signal:o});if(!r.ok){let e=`Network response was not ok (${r.status})`;try{e+=`: ${await r.text()||r.statusText}`}catch(d){e+=`: ${r.statusText}`}throw new Error(e)}let m="";if("ollama"!==a)throw new Error(`Unsupported host specified: ${a}`);{if(!r.body)throw new Error("Response body is null for Ollama");const e=r.body.getReader();let t,n;for(;c(),({value:n,done:t}=await e.read()),!t;){const t=(new TextDecoder).decode(n).split("\n").filter((e=>""!==e.trim()));for(const n of t){if("[DONE]"===n.trim())return o?.aborted&&e.cancel(),void l(m);try{const t=JSON.parse(n);if(t.message?.content&&(m+=t.message.content,i||s(m)),!0===t.done&&!i)return o?.aborted&&e.cancel(),void l(m)}catch(u){}}}o?.aborted&&e.cancel(),l(m)}}catch(u){o?.aborted||u instanceof Error&&"AbortError"===u.name?l("",!1):l(u instanceof Error?u.message:String(u),!0)}}}async function l(e,t){const s=new AbortController,n=t||s.signal,r=t?null:setTimeout((()=>s.abort()),12e3);try{const t=await fetch(e,{signal:n,method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",Accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8","Accept-Language":"en-US,en;q=0.9","Accept-Encoding":"gzip, deflate, br",DNT:"1",Connection:"keep-alive","Upgrade-Insecure-Requests":"1","Sec-Fetch-Dest":"document","Sec-Fetch-Mode":"navigate","Sec-Fetch-Site":"cross-site","Cache-Control":"max-age=0"}});if(r&&clearTimeout(r),n.aborted)throw new Error("Scraping aborted by user.");if(!t.ok)throw new Error(`Failed to fetch ${e} - Status: ${t.status}`);const s=t.headers.get("content-type");if(!s||!s.includes("text/html"))throw new Error(`Skipping non-HTML content (${s}) from ${e}`);const o=await t.text();return a(o)}catch(o){return r&&clearTimeout(r),"AbortError"===o.name?`[Scraping URL aborted: ${e}]`:`[Error scraping URL: ${e} - ${o.message}]`}}"undefined"!=typeof window&&(window.testGoogleSearchDebug=async function(e="test search"){const t={webMode:"Google",serpMaxLinksToVisit:2,webLimit:16,personas:{},persona:"Scholar",contextLimit:60,temperature:.7,maxTokens:32480,topP:.95,presencepenalty:0,models:[],chatMode:"web",ollamaUrl:"http://localhost:11434",ollamaConnected:!1,fontSize:14,panelOpen:!1,computeLevel:"low",userName:"user",userProfile:""};try{return await o(e,t)}catch(s){throw s}})},85141:(e,t,s)=>{s.d(t,{w:()=>z});var n=s(74848),r=s(36948),a=s(96540),o=s(3),i=s(32090),l=s(26532),c=s(45284);const d=()=>{const{config:e,updateConfig:t}=(0,r.UK)(),[s,d]=(0,a.useState)(e?.ollamaUrl||"http://localhost:11434"),[u,m]=(0,a.useState)(!1),h=()=>{m(!0),fetch(`${s}/api/tags`).then((e=>e.ok?e.json():e.json().then((t=>{throw new Error(t?.error||`Connection failed: ${e.status} ${e.statusText}`)})).catch((()=>{throw new Error(`Connection failed: ${e.status} ${e.statusText}`)})))).then((n=>{Array.isArray(n.models)?t({ollamaConnected:!0,ollamaUrl:s,ollamaError:void 0,models:(e?.models||[]).filter((e=>"ollama_generic"!==e.id)).concat([{id:"ollama_generic",host:"ollama",active:!0,name:"Ollama Model"}]),selectedModel:"ollama_generic"}):t(n?.error?{ollamaError:n.error,ollamaConnected:!1}:{ollamaError:"Unexpected response from Ollama",ollamaConnected:!1})})).catch((e=>{t({ollamaError:e.message,ollamaConnected:!1})})).finally((()=>{m(!1)}))},p=e?.ollamaConnected;return(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)(l.p,{id:"ollama-url-input",value:s,onChange:e=>d(e.target.value),placeholder:"http://localhost:11434",className:"pr-8",disabled:u}),!p&&(0,n.jsx)(i.$,{onClick:h,variant:"connect",size:"sm",disabled:u,children:u?"...":"Connect"}),p&&(0,n.jsx)(i.$,{variant:"ghost",size:"sm","aria-label":"Connected to Ollama",className:(0,c.cn)("w-8 rounded-md text-[var(--success)]"),disabled:u,onClick:h,children:(0,n.jsx)(o.YrT,{className:"h-5 w-5"})})]})};var u=s(29018),m=s(48698);const h=()=>{const{config:e,updateConfig:t}=(0,r.UK)(),{fetchAllModels:s}=(0,m.N)(),[l,d]=(0,a.useState)(!1),[h,p]=(0,a.useState)(null),x=async()=>{if(e?.ollamaUrl&&e?.ollamaConnected){d(!0),p(null);try{await s()}catch(t){const e=t instanceof Error?t.message:"Failed to refresh models";p(e)}finally{d(!1)}}else p("Ollama not connected")};(0,a.useEffect)((()=>{e?.ollamaConnected?x():p(null)}),[e?.ollamaConnected,e?.ollamaUrl]);const g=e?.ollamaConnected,f=e?.selectedModel,b=e?.models?.filter((e=>"ollama"===e.host))||[];return g?h?(0,n.jsx)("div",{className:"space-y-3",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center text-red-500",children:[(0,n.jsx)(o.y3G,{className:"mr-2 h-4 w-4"}),(0,n.jsx)("span",{className:"text-sm",children:h})]}),(0,n.jsx)(i.$,{onClick:x,variant:"ghost",size:"sm",disabled:l,className:"h-8 w-8 p-0",children:(0,n.jsx)(o.jTZ,{className:(0,c.cn)("h-4 w-4",l&&"animate-spin")})})]})}):0!==b.length||l?(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"flex-1",children:(0,n.jsxs)(u.l6,{value:f||"",onValueChange:e=>{t({selectedModel:e})},disabled:l||0===b.length,children:[(0,n.jsx)(u.bq,{variant:"settingsPanel",className:(0,c.cn)("w-full",l&&"opacity-50"),children:(0,n.jsx)(u.yv,{placeholder:l?"Loading models...":"Select a model..."})}),(0,n.jsx)(u.gC,{variant:"settingsPanel",children:b.map((e=>(0,n.jsx)(u.eb,{value:e.id,focusVariant:"activeTheme",className:"text-[var(--text)]",children:(0,n.jsxs)("div",{className:"flex flex-col",children:[(0,n.jsx)("span",{className:"font-medium",children:e.name||e.id}),e.context_length&&(0,n.jsxs)("span",{className:"text-xs text-[var(--text)]/60",children:["Context: ",e.context_length]})]})},e.id)))})]})}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[f&&b.some((e=>e.id===f))&&(0,n.jsx)("div",{className:"flex items-center text-[var(--success)]",children:(0,n.jsx)(o.YrT,{className:"h-4 w-4"})}),(0,n.jsx)(i.$,{onClick:x,variant:"ghost",size:"sm",disabled:l,className:"h-8 w-8 p-0 hover:bg-[var(--text)]/10",title:"Refresh models",children:(0,n.jsx)(o.jTZ,{className:(0,c.cn)("h-4 w-4",l&&"animate-spin")})})]})]}):(0,n.jsxs)("div",{className:"flex items-center justify-between py-2",children:[(0,n.jsxs)("div",{className:"flex items-center text-[var(--text)]/60",children:[(0,n.jsx)(o.y3G,{className:"mr-2 h-4 w-4"}),(0,n.jsx)("span",{className:"text-sm",children:"No models available"})]}),(0,n.jsx)(i.$,{onClick:x,variant:"ghost",size:"sm",disabled:l,className:"h-8 w-8 p-0",children:(0,n.jsx)(o.jTZ,{className:(0,c.cn)("h-4 w-4",l&&"animate-spin")})})]}):(0,n.jsxs)("div",{className:"flex items-center justify-center py-4 text-[var(--text)]/60",children:[(0,n.jsx)(o.y3G,{className:"mr-2 h-4 w-4"}),(0,n.jsx)("span",{className:"text-sm",children:"Connect to Ollama first"})]})},p=({title:e,Component:t})=>(0,n.jsxs)("div",{className:"px-6 py-4 border-b border-border/50 last:border-b-0",children:[(0,n.jsx)("div",{className:"flex items-center justify-between mb-3",children:(0,n.jsx)("h4",{className:"text-apple-body font-semibold text-foreground",children:e})}),(0,n.jsx)(t,{})]}),x=()=>(0,n.jsxs)("div",{className:(0,c.cn)("settings-card","bg-card border border-border rounded-xl shadow-sm","hover:shadow-md hover:border-border/80","overflow-hidden"),children:[(0,n.jsx)("div",{className:(0,c.cn)("settings-card-header","px-6 py-4 border-b border-border/50"),children:(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-apple-title3 font-semibold text-foreground",children:"API Access"}),(0,n.jsx)("p",{className:"text-apple-footnote text-muted-foreground",children:"Connect to your AI services"})]})}),(0,n.jsxs)("div",{className:"divide-y divide-border/50",children:[(0,n.jsx)(p,{Component:d,title:"Ollama"}),(0,n.jsx)(p,{Component:h,title:"Model Selection"})]})]});var g=s(6965),f=s(65634);const b=({size:e,updateConfig:t})=>(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)(f.J,{htmlFor:"contextLimit",className:"text-apple-body font-medium text-foreground",children:"Character Limit"}),(0,n.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:128===e?"Unlimited":`${e}k chars`})]}),(0,n.jsx)(g.A,{id:"contextLimit",value:[e],max:128,min:1,step:1,onValueChange:e=>t({contextLimit:e[0]}),className:"w-full"}),(0,n.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Maximum amount of page content to include in context. Higher values provide more context but use more tokens."})]}),v=()=>{const{config:e,updateConfig:t}=(0,r.UK)(),s=e?.contextLimit||1;return(0,n.jsxs)("div",{className:(0,c.cn)("settings-card","bg-card border border-border rounded-xl shadow-sm","hover:shadow-md hover:border-border/80","overflow-hidden"),children:[(0,n.jsx)("div",{className:(0,c.cn)("settings-card-header","px-6 py-4 border-b border-border/50"),children:(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-apple-title3 font-semibold text-foreground",children:"Page Context"}),(0,n.jsx)("p",{className:"text-apple-footnote text-muted-foreground",children:"Control how much page content to analyze"})]})}),(0,n.jsx)("div",{className:"p-6",children:(0,n.jsx)(b,{size:s,updateConfig:t})})]})},w=()=>{const{config:e,updateConfig:t}=(0,r.UK)(),s=e=>s=>{const n=Array.isArray(s)?s[0]:s;t({[e]:n})},a=e.temperature??.7,o=e.maxTokens??32048,i=e.topP??.95,d=e.presencepenalty??0;return(0,n.jsxs)("div",{className:(0,c.cn)("settings-card","bg-card border border-border rounded-xl shadow-sm","hover:shadow-md hover:border-border/80","overflow-hidden"),children:[(0,n.jsx)("div",{className:(0,c.cn)("settings-card-header","px-6 py-4 border-b border-border/50"),children:(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-apple-title3 font-semibold text-foreground",children:"Model Parameters"}),(0,n.jsx)("p",{className:"text-apple-footnote text-muted-foreground",children:"Fine-tune AI model behavior"})]})}),(0,n.jsxs)("div",{className:"p-6 space-y-6",children:[(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)(f.J,{htmlFor:"temperature",className:"text-apple-body font-medium text-foreground",children:"Temperature"}),(0,n.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:a.toFixed(2)})]}),(0,n.jsx)(g.A,{id:"temperature",min:0,max:2,step:.01,value:[a],onValueChange:s("temperature"),className:"w-full"}),(0,n.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Controls randomness. Lower values make responses more focused and deterministic."})]}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)(f.J,{htmlFor:"maxTokens",className:"text-apple-body font-medium text-foreground",children:"Max Tokens"}),(0,n.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:o.toLocaleString()})]}),(0,n.jsx)(l.p,{id:"maxTokens",type:"number",value:o,onChange:e=>s("maxTokens")(parseInt(e.target.value)||0),className:"w-full",min:1,max:1e5}),(0,n.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Maximum length of the response in tokens."})]}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)(f.J,{htmlFor:"topP",className:"text-apple-body font-medium text-foreground",children:"Top P"}),(0,n.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:i.toFixed(2)})]}),(0,n.jsx)(g.A,{id:"topP",min:0,max:1,step:.01,value:[i],onValueChange:s("topP"),className:"w-full"}),(0,n.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Controls diversity via nucleus sampling. Lower values focus on more likely tokens."})]}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)(f.J,{htmlFor:"presencePenalty",className:"text-apple-body font-medium text-foreground",children:"Presence Penalty"}),(0,n.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:d.toFixed(2)})]}),(0,n.jsx)(g.A,{id:"presencePenalty",min:-2,max:2,step:.01,value:[d],onValueChange:s("presencepenalty"),className:"w-full"}),(0,n.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Reduces repetition by penalizing tokens that have already appeared."})]})]})]})};var y=s(77086),j=s(5876),N=s(89696);const C=({hasChange:e,onSave:t,onSaveAs:s,onCancel:r})=>e?(0,n.jsxs)("div",{className:"flex mt-4 space-x-2 justify-end w-full",children:[(0,n.jsx)(i.$,{variant:"default",size:"sm",onClick:t,className:"bg-primary text-primary-foreground hover:bg-primary/90",children:"Save"}),(0,n.jsx)(i.$,{variant:"outline",size:"sm",onClick:s,children:"Save As..."}),(0,n.jsx)(i.$,{variant:"outline",size:"sm",onClick:r,children:"Cancel"})]}):null,k=({isOpen:e,onOpenChange:t,personaPrompt:s,personas:r,updateConfig:o,onModalClose:c})=>{const[d,u]=(0,a.useState)("");return(0,n.jsx)(y.lG,{open:e,onOpenChange:t,children:(0,n.jsxs)(y.Cf,{className:"sm:max-w-[425px]",onCloseAutoFocus:e=>e.preventDefault(),children:[(0,n.jsx)(y.c7,{children:(0,n.jsx)(y.L3,{children:"Create New Persona"})}),(0,n.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,n.jsx)(f.J,{htmlFor:"persona-name",className:"text-base font-medium text-foreground sr-only",children:"Persona Name"}),(0,n.jsx)(l.p,{id:"persona-name",placeholder:"Enter persona name",value:d,onChange:e=>u(e.target.value)})]}),(0,n.jsxs)(y.Es,{className:"sm:justify-end",children:[(0,n.jsx)(i.$,{type:"button",variant:"outline",size:"sm",onClick:c,children:"Cancel"}),(0,n.jsx)(i.$,{type:"button",variant:"default",size:"sm",disabled:!d.trim(),onClick:()=>{d.trim()&&(o({personas:{...r,[d.trim()]:s},persona:d.trim()}),u(""),c())},children:"Create"})]})]})})},S=({isOpen:e,onOpenChange:t,persona:s,personas:r,updateConfig:a,onModalClose:o})=>(0,n.jsx)(y.lG,{open:e,onOpenChange:t,children:(0,n.jsxs)(y.Cf,{className:"sm:max-w-[425px]",children:[(0,n.jsxs)(y.c7,{children:[(0,n.jsxs)(y.L3,{children:['Delete "',s,'"']}),(0,n.jsx)(y.rr,{className:"pt-2",children:"Are you sure you want to delete this persona? This action cannot be undone."})]}),(0,n.jsxs)(y.Es,{className:"sm:justify-end pt-4",children:[(0,n.jsx)(i.$,{type:"button",variant:"outline",size:"sm",onClick:o,children:"Cancel"}),(0,n.jsx)(i.$,{type:"button",variant:"destructive",size:"sm",onClick:()=>{const e={...r};delete e[s];const t=Object.keys(e);a({personas:e,persona:t.length>0?t[0]:"Scholar"}),o()},children:"Delete"})]})]})}),$=({personas:e,persona:t,updateConfig:s})=>(0,n.jsxs)(u.l6,{value:t,onValueChange:e=>s({persona:e}),children:[(0,n.jsx)(u.bq,{className:"flex w-full",children:(0,n.jsx)(u.yv,{placeholder:"Select persona"})}),(0,n.jsx)(u.gC,{children:Object.keys(e).map((e=>(0,n.jsx)(u.eb,{value:e,children:e},e)))})]}),M=({personaPrompt:e,setPersonaPrompt:t,isEditing:s,setIsEditing:r})=>{const a={onFocus:e=>{s||r(!0)}};return(0,n.jsx)(N.T,{value:e,onChange:e=>{s||r(!0),t(e.target.value)},readOnly:!s,...a,placeholder:"Define the persona's characteristics and instructions here...",className:(0,c.cn)("w-full min-h-[120px] resize-none",s?"hover:border-primary focus:border-primary":"opacity-75 cursor-default"),rows:5})},E=()=>{const{config:e,updateConfig:t}=(0,r.UK)(),[s,o]=(0,a.useState)(!1),[l,d]=(0,a.useState)(!1),[u,m]=(0,a.useState)(!1),h=e?.personas||{Scholar:"You are The Scholar, an analytical academic researcher specializing in web search analysis."},p=e?.persona||"Scholar",x=h?.[p]??h?.Scholar??"You are The Scholar, an analytical academic researcher specializing in web search analysis.",[g,f]=(0,a.useState)(x),b=u&&g!==x;(0,a.useEffect)((()=>{f(h?.[p]??h?.Scholar??""),m(!1)}),[p,JSON.stringify(h)]);return(0,n.jsxs)("div",{className:(0,c.cn)("settings-card","bg-card border border-border rounded-xl shadow-sm","hover:shadow-md hover:border-border/80","overflow-hidden"),children:[(0,n.jsx)("div",{className:(0,c.cn)("settings-card-header","px-6 py-4 border-b border-border/50"),children:(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-apple-title3 font-semibold text-foreground",children:"Persona"}),(0,n.jsx)("p",{className:"text-apple-footnote text-muted-foreground",children:"Customize AI personality and behavior"})]})}),(0,n.jsxs)("div",{className:"p-6 space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("div",{className:"flex-1",children:(0,n.jsx)($,{persona:p,personas:h,updateConfig:t})}),(0,n.jsx)(i.$,{variant:"ghost",size:"sm","aria-label":"Add new persona",className:"p-2 h-10 w-10",onClick:()=>{f(""),m(!0),o(!0)},children:(0,n.jsx)(j.YHj,{className:"h-5 w-5"})}),Object.keys(h).length>1&&(0,n.jsx)(i.$,{variant:"ghost",size:"sm","aria-label":"Delete current persona",className:"p-2 h-10 w-10 hover:text-destructive hover:bg-destructive/10",onClick:()=>d(!0),children:(0,n.jsx)(j.dW_,{className:"h-5 w-5"})})]}),(0,n.jsx)(M,{personaPrompt:g,setPersonaPrompt:f,isEditing:u,setIsEditing:m}),(0,n.jsx)(C,{hasChange:b,onSave:()=>{t({personas:{...h,[p]:g}}),m(!1)},onSaveAs:()=>{o(!0)},onCancel:()=>{f(x),m(!1)}})]}),(0,n.jsx)(k,{isOpen:s,onOpenChange:e=>{o(e),e||(f(x),m(!1))},personaPrompt:g,personas:h,updateConfig:t,onModalClose:()=>o(!1)}),(0,n.jsx)(S,{isOpen:l,onOpenChange:d,persona:p,personas:h,updateConfig:t,onModalClose:()=>d(!1)})]})};var A=s(67311);const P=({webMode:e,updateConfig:t})=>(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)(f.J,{className:"text-apple-body font-medium text-foreground",children:"Search Provider"}),(0,n.jsx)(A.z,{value:e,onValueChange:e=>t({webMode:e}),className:"space-y-2",children:["Google"].map((e=>(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(A.C,{value:e,id:`webMode-${e}`}),(0,n.jsx)(f.J,{htmlFor:`webMode-${e}`,className:"text-apple-body font-medium cursor-pointer",children:e})]},e)))})]}),T=({config:e,updateConfig:t})=>{const s=e?.webLimit??16,r=e?.serpMaxLinksToVisit??3;return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)(f.J,{htmlFor:"maxLinks",className:"text-apple-body font-medium text-foreground",children:"Max Links to Visit"}),(0,n.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:r})]}),(0,n.jsx)(g.A,{id:"maxLinks",value:[r],max:10,min:1,step:1,onValueChange:e=>t({serpMaxLinksToVisit:e[0]}),className:"w-full"}),(0,n.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Number of search result links to fetch and analyze."})]}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)(f.J,{htmlFor:"charLimit",className:"text-apple-body font-medium text-foreground",children:"Content Character Limit"}),(0,n.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:128===s?"Unlimited":`${s}k chars`})]}),(0,n.jsx)(g.A,{id:"charLimit",value:[s],max:128,min:1,step:1,onValueChange:e=>t({webLimit:e[0]}),className:"w-full"}),(0,n.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Maximum characters (in thousands) of content to analyze per page. Set to 128k for unlimited."})]})]})},L=()=>{const{config:e,updateConfig:t}=(0,r.UK)();return(0,a.useEffect)((()=>{if("Google"===e?.webMode){const s={};void 0===e?.serpMaxLinksToVisit&&(s.serpMaxLinksToVisit=3),void 0===e?.webLimit&&(s.webLimit=16),Object.keys(s).length>0&&t(s)}}),[e?.webMode,e?.serpMaxLinksToVisit,e?.webLimit,t]),(0,n.jsxs)("div",{className:(0,c.cn)("settings-card","bg-card border border-border rounded-xl shadow-sm","hover:shadow-md hover:border-border/80","overflow-hidden"),children:[(0,n.jsx)("div",{className:(0,c.cn)("settings-card-header","px-6 py-4 border-b border-border/50"),children:(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-apple-title3 font-semibold text-foreground",children:"Web Search"}),(0,n.jsx)("p",{className:"text-apple-footnote text-muted-foreground",children:"Configure search behavior and limits"})]})}),(0,n.jsxs)("div",{className:"p-6 space-y-6",children:[(0,n.jsx)(P,{updateConfig:t,webMode:e?.webMode}),"Google"===e?.webMode&&(0,n.jsx)("div",{className:"pt-2 border-t border-border/50",children:(0,n.jsx)(T,{config:e,updateConfig:t})}),!e?.webMode&&(0,n.jsx)("div",{className:"pt-2 border-t border-border/50",children:(0,n.jsx)("p",{className:"text-muted-foreground text-apple-body",children:"Select a search provider to configure its settings."})})]})]})},z=()=>{const{config:e}=(0,r.UK)(),[t,s]=(0,a.useState)(!e?.models||0===e.models.length);return(0,n.jsxs)("div",{id:"settings",className:"relative z-[1] top-0 w-full h-full flex-1 flex-col overflow-y-auto overflow-x-hidden bg-background text-foreground px-6 pb-10 pt-6 scrollbar-hidden",children:[(0,n.jsx)("div",{className:"flex justify-center mb-6",children:(0,n.jsx)("div",{id:"kofi-widget",children:(0,n.jsx)("a",{href:"https://ko-fi.com/T6T11G2CYS",target:"_blank",rel:"noopener noreferrer",children:(0,n.jsx)("img",{height:"36",style:{border:"0px",height:"36px"},src:"https://storage.ko-fi.com/cdn/kofi6.png?v=6",alt:"Buy Me a Coffee at ko-fi.com"})})})}),t&&(0,n.jsx)("div",{className:(0,c.cn)("mb-8 p-6","rounded-xl","bg-card border border-border shadow-sm","text-foreground"),children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,n.jsx)("h2",{className:"text-apple-title3 font-semibold text-center",children:"Quick Setup Guide"}),(0,n.jsxs)("div",{className:"flex flex-col gap-4 w-full",children:[(0,n.jsxs)("div",{className:"flex items-start gap-3",children:[(0,n.jsx)("span",{className:"flex-shrink-0 w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-apple-footnote font-medium",children:"1"}),(0,n.jsx)("p",{className:"text-apple-callout text-muted-foreground",children:"Fill your API key or URLs in API Access"})]}),(0,n.jsxs)("div",{className:"flex items-start gap-3",children:[(0,n.jsx)("span",{className:"flex-shrink-0 w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-apple-footnote font-medium",children:"2"}),(0,n.jsx)("p",{className:"text-apple-callout text-muted-foreground",children:"Exit settings, then use the persona selector to choose your model and start chatting"})]}),(0,n.jsx)("div",{className:"text-apple-footnote text-muted-foreground mt-2 ml-9 italic",children:"Note: You can change other settings now or later. Have fun!"})]}),(0,n.jsx)(i.$,{variant:"default",className:"bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg px-6 py-2 text-apple-callout font-medium",onClick:()=>{s(!1)},children:"Get Started"})]})}),(0,n.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,n.jsx)(x,{}),(0,n.jsx)(w,{}),(0,n.jsx)(E,{}),(0,n.jsx)(v,{}),(0,n.jsx)(L,{}),(0,n.jsx)("div",{className:"pointer-events-none h-8"})]})]})}},86108:(e,t,s)=>{s.d(t,{z2:()=>d});var n,r,a=s(75923);!function(e){e.Default="default",e.ConfirmDeleteCard="confirmDeleteCard"}(n||(n={})),function(e){e.Default="default"}(r||(r={}));const o={isOpen:!1},i=(0,a.Z0)({name:"sidePanel",initialState:o,reducers:{reset:()=>o}}),{actions:l,reducer:c}=i,d={}},86174:(e,t,s)=>{var n;s.d(t,{A:()=>r}),function(e){e.ContentPort="content",e.SidePanelPort="sidePanel",e.SAVE_NOTE_TO_FILE="save-note-to-file"}(n||(n={}));const r=n}},d={};function u(e){var t=d[e];if(void 0!==t)return t.exports;var s=d[e]={exports:{}};return c[e].call(s.exports,s,s.exports,u),s.exports}u.m=c,e="function"==typeof Symbol?Symbol("webpack queues"):"__webpack_queues__",t="function"==typeof Symbol?Symbol("webpack exports"):"__webpack_exports__",s="function"==typeof Symbol?Symbol("webpack error"):"__webpack_error__",n=e=>{e&&e.d<1&&(e.d=1,e.forEach((e=>e.r--)),e.forEach((e=>e.r--?e.r++:e())))},u.a=(r,a,o)=>{var i;o&&((i=[]).d=-1);var l,c,d,u=new Set,m=r.exports,h=new Promise(((e,t)=>{d=t,c=e}));h[t]=m,h[e]=e=>(i&&e(i),u.forEach(e),h.catch((e=>{}))),r.exports=h,a((r=>{var a;l=(r=>r.map((r=>{if(null!==r&&"object"==typeof r){if(r[e])return r;if(r.then){var a=[];a.d=0,r.then((e=>{o[t]=e,n(a)}),(e=>{o[s]=e,n(a)}));var o={};return o[e]=e=>e(a),o}}var i={};return i[e]=e=>{},i[t]=r,i})))(r);var o=()=>l.map((e=>{if(e[s])throw e[s];return e[t]})),c=new Promise((t=>{(a=()=>t(o)).r=0;var s=e=>e!==i&&!u.has(e)&&(u.add(e),e&&!e.d&&(a.r++,e.push(a)));l.map((t=>t[e](s)))}));return a.r?c:o()}),(e=>(e?d(h[s]=e):c(m),n(i)))),i&&i.d<0&&(i.d=0)},r=[],u.O=(e,t,s,n)=>{if(!t){var a=1/0;for(c=0;c<r.length;c++){for(var[t,s,n]=r[c],o=!0,i=0;i<t.length;i++)(!1&n||a>=n)&&Object.keys(u.O).every((e=>u.O[e](t[i])))?t.splice(i--,1):(o=!1,n<a&&(a=n));if(o){r.splice(c--,1);var l=s();void 0!==l&&(e=l)}}return e}n=n||0;for(var c=r.length;c>0&&r[c-1][2]>n;c--)r[c]=r[c-1];r[c]=[t,s,n]},u.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return u.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,u.t=function(e,t){if(1&t&&(e=this(e)),8&t)return e;if("object"==typeof e&&e){if(4&t&&e.__esModule)return e;if(16&t&&"function"==typeof e.then)return e}var s=Object.create(null);u.r(s);var n={};a=a||[null,o({}),o([]),o(o)];for(var r=2&t&&e;"object"==typeof r&&!~a.indexOf(r);r=o(r))Object.getOwnPropertyNames(r).forEach((t=>n[t]=()=>e[t]));return n.default=()=>e,u.d(s,n),s},u.d=(e,t)=>{for(var s in t)u.o(t,s)&&!u.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},u.f={},u.e=e=>Promise.all(Object.keys(u.f).reduce(((t,s)=>(u.f[s](e,t),t)),[])),u.u=e=>"assets/pdf.js",u.miniCssF=e=>{},u.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),u.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i={},l="chromepanion:",u.l=(e,t,s,n)=>{if(i[e])i[e].push(t);else{var r,a;if(void 0!==s)for(var o=document.getElementsByTagName("script"),c=0;c<o.length;c++){var d=o[c];if(d.getAttribute("src")==e||d.getAttribute("data-webpack")==l+s){r=d;break}}r||(a=!0,(r=document.createElement("script")).charset="utf-8",r.timeout=120,u.nc&&r.setAttribute("nonce",u.nc),r.setAttribute("data-webpack",l+s),r.src=e),i[e]=[t];var m=(t,s)=>{r.onerror=r.onload=null,clearTimeout(h);var n=i[e];if(delete i[e],r.parentNode&&r.parentNode.removeChild(r),n&&n.forEach((e=>e(s))),t)return t(s)},h=setTimeout(m.bind(null,void 0,{type:"timeout",target:r}),12e4);r.onerror=m.bind(null,r.onerror),r.onload=m.bind(null,r.onload),a&&document.head.appendChild(r)}},u.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},u.j=808,u.p="./",(()=>{var e={808:0};u.f.j=(t,s)=>{var n=u.o(e,t)?e[t]:void 0;if(0!==n)if(n)s.push(n[2]);else{var r=new Promise(((s,r)=>n=e[t]=[s,r]));s.push(n[2]=r);var a=u.p+u.u(t),o=new Error;u.l(a,(s=>{if(u.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var r=s&&("load"===s.type?"missing":s.type),a=s&&s.target&&s.target.src;o.message="Loading chunk "+t+" failed.\n("+r+": "+a+")",o.name="ChunkLoadError",o.type=r,o.request=a,n[1](o)}}),"chunk-"+t,t)}},u.O.j=t=>0===e[t];var t=(t,s)=>{var n,r,[a,o,i]=s,l=0;if(a.some((t=>0!==e[t]))){for(n in o)u.o(o,n)&&(u.m[n]=o[n]);if(i)var c=i(u)}for(t&&t(s);l<a.length;l++)r=a[l],u.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return u.O(c)},s=globalThis.webpackChunkchromepanion=globalThis.webpackChunkchromepanion||[];s.forEach(t.bind(null,0)),s.push=t.bind(null,s.push.bind(s))})(),u.nc=void 0;var m=u.O(void 0,[484,609,38,845,407,351,83,963,3,6,349,797,387,667,866,169,550,55,544,756,131,436,290,994,805,259,740,358,177,602,283,810,204,896,802,613,447,523],(()=>u(53003)));m=u.O(m)})();
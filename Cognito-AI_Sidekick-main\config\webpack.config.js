const CopyPlugin = require('copy-webpack-plugin');
const GenerateJsonFromJsPlugin = require('generate-json-from-js-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const dotenv = require('dotenv');
const webpack = require('webpack');
const { join } = require('path');
const { inDev } = require("./helpers");
const packageJson = require('../package.json');

dotenv.config();

const browsers = [
  'chrome'
];

const Root = join(__dirname, '..');
const Source = join(Root, 'src');
const Dist = join(Root, 'dist');

const Public = join(Root, 'public');
const Background = join(Source, 'background');
const Content = join(Source, 'content');
const SidePanel = join(Source, 'sidePanel');
const Lib = join(Source, 'lib');
const Options = join(Source, 'options');

const config = {
  mode: process.env.NODE_ENV,
  target: 'web',
  devtool: inDev() ? 'cheap-module-source-map' : false,
  entry: {
    background: join(Background, 'index.ts'),
    content: join(Content, 'index.tsx'),
    app: join(SidePanel, 'index.tsx')
  },
  module: { rules: require('./rules') },
  resolve: {
    extensions: ['.js', '.ts', '.jsx', '.tsx', '.css'],
    alias: {
      lib: Lib,
      '@': Root,
      background: Background,
      sidePanel: SidePanel,
      '@/utils': join(Background, 'util.ts'),
      content: Content,
      assets: Public,
      options: Options,
      ...require('./aliases')
    },
    // Optimize module resolution
    mainFields: ['browser', 'module', 'main'],
    // Prefer ES modules for better tree shaking
    preferRelative: true
  },
  // Performance optimization settings
  performance: {
    maxAssetSize: 500000, // 500 KiB
    maxEntrypointSize: 500000, // 500 KiB
    hints: inDev() ? false : 'warning'
  },
  plugins: [
    ...require('./plugins'),
    new webpack.DefinePlugin({
      'APP_VERSION': JSON.stringify(packageJson.version)
    }),
    new HtmlWebpackPlugin(
        {
          inject: 'body',
          template: join(SidePanel, 'index.html'),
          filename: 'assets/sidePanel.html',
          chunks: ['app']
        }),
    ...browsers.map(browser => new GenerateJsonFromJsPlugin({
          path: join(__dirname, 'manifest', 'v3.js'),
          filename: 'manifest.json',
          options: {
            replacer: (key, value) => {
              switch (key) {
                case 'extension_pages':
                  return value.replace(/\s+/g, ' ');

                default:
                  return value;
              }
            }
          }
    })),
    new CopyPlugin({
      patterns: [
        {
          from: Public,
          to: 'assets',
          globOptions: {
            ignore: [
              // Add specific PNG files you want to exclude here
              // Example: '**/images/unwanted-file.png',
              // '**/images/another-file.png',
            ]
          }
        },
        // Copy PDF worker conditionally for fallback
        {
          from: require.resolve('pdfjs-dist/build/pdf.worker.mjs'),
          to: 'pdf.worker.mjs'
        }
      ]
    }),
    // Bundle analyzer (only when ANALYZE=true)
    ...(process.env.ANALYZE ? [new BundleAnalyzerPlugin({
      analyzerMode: 'server',
      openAnalyzer: true,
      generateStatsFile: true,
      statsFilename: 'bundle-stats.json'
    })] : [])
  ],
  optimization: {
    // Enable tree shaking
    usedExports: true,
    sideEffects: false,
    // Minimize in production
    minimize: !inDev(),
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: !inDev(), // Remove console.logs in production
            drop_debugger: true,
            pure_funcs: ['console.log', 'console.info', 'console.debug']
          },
          mangle: {
            safari10: true
          },
          format: {
            comments: false
          }
        },
        extractComments: false
      })
    ],
    splitChunks: {
      chunks: 'all',
      minSize: 20000,
      maxSize: 244000, // 244 KiB to stay under warning threshold
      cacheGroups: {
        // React and React-DOM in separate chunk
        react: {
          test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
          name: 'assets/react',
          chunks: chunk => chunk.name !== "background",
          priority: 20
        },
        // Radix UI components
        radix: {
          test: /[\\/]node_modules[\\/]@radix-ui[\\/]/,
          name: 'assets/radix',
          chunks: chunk => chunk.name !== "background",
          priority: 15
        },
        // Motion library
        motion: {
          test: /[\\/]node_modules[\\/](motion|framer-motion)[\\/]/,
          name: 'assets/motion',
          chunks: chunk => chunk.name !== "background",
          priority: 15
        },
        // Icons (separate chunk for better caching)
        icons: {
          test: /[\\/]node_modules[\\/](lucide-react|react-icons)[\\/]/,
          name: 'assets/icons',
          chunks: chunk => chunk.name !== "background",
          priority: 15
        },
        // PDF.js
        pdf: {
          test: /[\\/]node_modules[\\/]pdfjs-dist[\\/]/,
          name: 'assets/pdf',
          chunks: chunk => chunk.name !== "background",
          priority: 15
        },
        // Markdown processing
        markdown: {
          test: /[\\/]node_modules[\\/](react-markdown|remark-|micromark|mdast-|hast-|unist-)[\\/]/,
          name: 'assets/markdown',
          chunks: chunk => chunk.name !== "background",
          priority: 10
        },
        // Other vendor libraries
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'assets/vendor',
          chunks: chunk => chunk.name !== "background",
          priority: 5
        }
      }
    }
  }
};

const buildConfig = browser => ({
  ...config,
  name: browser,
  output: {
    path: join(Dist, browser),
    filename: '[name].js',
    publicPath: process.env.EXTENSION_PUBLIC_PATH || './',
    clean: true,
    globalObject: 'globalThis'
  }
});

module.exports = buildConfig(process.env.BROWSER || 'chrome');
